/**
 * IME Plugin for Lexical
 * Provides better Chinese input method support
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';

interface IMEPluginProps {
    enabled?: boolean;
    debug?: boolean;
}

export default function IMEPlugin({ 
    enabled = true, 
    debug = false 
}: IMEPluginProps = {}): null {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        if (!enabled) return;

        const rootElement = editor.getRootElement();
        if (!rootElement) return;

        let isComposing = false;
        let compositionData = '';

        const handleCompositionStart = (event: CompositionEvent) => {
            isComposing = true;
            compositionData = '';
            
            if (debug) {
                console.log('IME: Composition started', event);
            }
        };

        const handleCompositionUpdate = (event: CompositionEvent) => {
            if (isComposing) {
                compositionData = event.data || '';
                
                if (debug) {
                    console.log('IME: Composition update', event.data);
                }
            }
        };

        const handleCompositionEnd = (event: CompositionEvent) => {
            isComposing = false;
            compositionData = '';
            
            if (debug) {
                console.log('IME: Composition ended', event.data);
            }
        };

        const handleBeforeInput = (event: InputEvent) => {
            // Let composition events handle IME input
            if (isComposing) {
                if (debug) {
                    console.log('IME: Blocking beforeinput during composition', event);
                }
                return;
            }
        };

        const handleInput = (event: InputEvent) => {
            // Additional input handling if needed
            if (debug && isComposing) {
                console.log('IME: Input during composition', event);
            }
        };

        // Add event listeners
        rootElement.addEventListener('compositionstart', handleCompositionStart);
        rootElement.addEventListener('compositionupdate', handleCompositionUpdate);
        rootElement.addEventListener('compositionend', handleCompositionEnd);
        rootElement.addEventListener('beforeinput', handleBeforeInput);
        rootElement.addEventListener('input', handleInput);

        // Cleanup
        return () => {
            rootElement.removeEventListener('compositionstart', handleCompositionStart);
            rootElement.removeEventListener('compositionupdate', handleCompositionUpdate);
            rootElement.removeEventListener('compositionend', handleCompositionEnd);
            rootElement.removeEventListener('beforeinput', handleBeforeInput);
            rootElement.removeEventListener('input', handleInput);
        };
    }, [editor, enabled, debug]);

    return null;
}
