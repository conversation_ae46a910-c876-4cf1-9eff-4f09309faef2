"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 🔧 调试：检查onChange回调是否正确传递\n    console.log(\"\\uD83D\\uDD27 LexicalEditor 初始化:\", {\n        hasOnChange: !!onChange,\n        readOnly: readOnly,\n        valueLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n        mounted: mounted,\n        timestamp: new Date().toISOString()\n    });\n    // 🔧 检查全局事件监听器\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        if (true) {\n            console.log(\"\\uD83D\\uDD27 检查全局事件监听器...\");\n            // 检查是否有全局的beforeinput监听器\n            var testElement = document.createElement(\"div\");\n            testElement.contentEditable = \"true\";\n            document.body.appendChild(testElement);\n            var testEvent = new InputEvent(\"beforeinput\", {\n                inputType: \"insertText\",\n                data: \"test\",\n                bubbles: true,\n                cancelable: true\n            });\n            testElement.addEventListener(\"beforeinput\", function(e) {\n                console.log(\"\\uD83D\\uDD27 测试元素接收到beforeinput事件:\", e);\n            });\n            console.log(\"\\uD83D\\uDD27 派发测试beforeinput事件...\");\n            var result = testElement.dispatchEvent(testEvent);\n            console.log(\"\\uD83D\\uDD27 测试事件结果:\", {\n                result: result,\n                defaultPrevented: testEvent.defaultPrevented\n            });\n            document.body.removeChild(testElement);\n        }\n    }, []);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 🔧 修复：创建有效的初始编辑器状态\n        editorState: function() {\n            var editor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                namespace: \"LexicalEditor\",\n                nodes: [\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                    _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                    _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                ],\n                onError: function(error) {\n                    return console.error(\"Lexical Error:\", error);\n                }\n            });\n            // 创建基本的编辑器状态\n            return editor.parseEditorState({\n                root: {\n                    children: [\n                        {\n                            children: [],\n                            direction: null,\n                            format: \"\",\n                            indent: 0,\n                            type: \"paragraph\",\n                            version: 1\n                        }, \n                    ],\n                    direction: null,\n                    format: \"\",\n                    indent: 0,\n                    type: \"root\",\n                    version: 1\n                }\n            });\n        }\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        console.log(\"\\uD83D\\uDEA8 handleChange被触发!\", {\n            hasOnChange: !!onChange,\n            tags: Array.from(tags),\n            editorStateExists: !!editorState,\n            timestamp: new Date().toISOString()\n        });\n        if (!onChange) {\n            console.log(\"❌ onChange函数不存在，无法处理编辑器变化\");\n            return;\n        }\n        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n        if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n            console.log(\"⏭️ 跳过历史合并或内容同步触发的更新\");\n            return;\n        }\n        console.log(\"✅ 开始处理编辑器状态变化\");\n        editorState.read(function() {\n            try {\n                // 🔧 添加调试：检查编辑器状态中的列表结构\n                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                var children = root.getChildren();\n                console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                children.forEach(function(child, index) {\n                    console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素:\"), {\n                        type: child.getType(),\n                        textContent: child.getTextContent()\n                    });\n                    if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                        console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                            type: child.getListType(),\n                            childrenCount: child.getChildren().length\n                        });\n                        var listItems = child.getChildren();\n                        listItems.forEach(function(item, itemIndex) {\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                var itemChildren = item.getChildren();\n                                console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                    childrenCount: itemChildren.length,\n                                    textContent: item.getTextContent(),\n                                    hasNestedList: itemChildren.some(function(c) {\n                                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                    })\n                                });\n                            }\n                        });\n                    }\n                });\n                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                var editorStateJSON = JSON.stringify(editorState.toJSON());\n                console.log(\"\\uD83D\\uDD27 准备调用onChange，JSON长度:\", editorStateJSON.length);\n                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑\n                onChange(function() {\n                    console.log(\"\\uD83D\\uDD27 onChange回调被执行，返回JSON内容\");\n                    return editorStateJSON;\n                });\n                console.log(\"✅ onChange调用完成\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D Error in handleChange:\", error);\n            // 如果转换出错，保持原有内容不变\n            }\n        });\n    }, [\n        onChange,\n        value\n    ]);\n    // 调试插件 - 检查编辑器状态\n    var DebugPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器初始化完成\", {\n                isEditable: editor.isEditable(),\n                hasRootElement: !!editor.getRootElement(),\n                readOnly: readOnly,\n                timestamp: new Date().toISOString()\n            });\n            // 🔧 确保编辑器在非只读模式下是可编辑的\n            if (!readOnly && !editor.isEditable()) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 强制设置编辑器为可编辑状态\");\n                editor.setEditable(true);\n            }\n            // 监听所有编辑器更新\n            var removeUpdateListener = editor.registerUpdateListener(function(param) {\n                var editorState = param.editorState, prevEditorState = param.prevEditorState, tags = param.tags;\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器更新\", {\n                    tags: Array.from(tags),\n                    hasChanges: editorState !== prevEditorState,\n                    isEditable: editor.isEditable(),\n                    timestamp: new Date().toISOString()\n                });\n            });\n            // 监听编辑器状态变化\n            var removeEditableListener = editor.registerEditableListener(function(editable) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器可编辑状态变化\", {\n                    editable: editable,\n                    readOnly: readOnly\n                });\n            });\n            return function() {\n                removeUpdateListener();\n                removeEditableListener();\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(DebugPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                console.log(\"\\uD83D\\uDD27 ListExitPlugin: Enter键被按下\");\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 优化版本，避免与用户输入冲突\n    var ContentSyncPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        var _$ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), lastSyncedValue = _$ref1[0], setLastSyncedValue = _$ref1[1];\n        var _$ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUserTyping = _$ref2[0], setIsUserTyping = _$ref2[1];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 🔧 优化：只有当value真正改变且用户没有在输入时才同步\n                if (value !== lastSyncedValue && !isUserTyping) {\n                    console.log(\"\\uD83D\\uDD27 ContentSyncPlugin: 准备同步内容\", {\n                        valueLength: value.length,\n                        lastSyncedLength: lastSyncedValue.length,\n                        isUserTyping: isUserTyping\n                    });\n                    // 使用setTimeout来避免在渲染过程中调用flushSync\n                    setTimeout(function() {\n                        if (value.trim()) {\n                            try {\n                                // 🔧 解析JSON格式的编辑器状态\n                                var editorStateData = JSON.parse(value);\n                                console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                                // 直接设置编辑器状态\n                                var newEditorState = editor.parseEditorState(editorStateData);\n                                editor.setEditorState(newEditorState);\n                                setLastSyncedValue(value);\n                            } catch (jsonError) {\n                                console.error(\"\\uD83D\\uDD27 JSON解析失败，尝试作为Markdown处理:\", jsonError);\n                                // 🔧 修复：如果不是JSON格式，尝试作为Markdown或纯文本处理\n                                if (value.trim() === \"\\n\" || value.trim() === \"\") {\n                                    // 空内容，创建空段落\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                } else {\n                                    // 有内容但不是JSON，可能是旧的Markdown格式\n                                    console.log(\"\\uD83D\\uDD27 检测到非JSON内容，可能是旧格式，创建空编辑器\");\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                }\n                            }\n                        } else {\n                            // 空内容时清空并创建一个空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                            setLastSyncedValue(value);\n                        }\n                    }, 0);\n                }\n            }\n        }, [\n            editor,\n            value,\n            mounted,\n            lastSyncedValue,\n            isUserTyping\n        ]);\n        // 🔧 监听用户输入状态\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (!editor) return;\n            var rootElement = editor.getRootElement();\n            if (!rootElement) return;\n            var typingTimer;\n            var handleInput = function() {\n                setIsUserTyping(true);\n                clearTimeout(typingTimer);\n                typingTimer = setTimeout(function() {\n                    setIsUserTyping(false);\n                }, 1000); // 1秒后认为用户停止输入\n            };\n            rootElement.addEventListener(\"input\", handleInput);\n            rootElement.addEventListener(\"keydown\", handleInput);\n            return function() {\n                rootElement.removeEventListener(\"input\", handleInput);\n                rootElement.removeEventListener(\"keydown\", handleInput);\n                clearTimeout(typingTimer);\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(ContentSyncPlugin, \"SYK262ymxGBaV3Gm+LQPP5D2LGU=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 🔧 新增：DOM状态检查插件\n    var DOMStatePlugin = function() {\n        _s4();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            var checkDOMState = function() {\n                var rootElement = editor.getRootElement();\n                if (rootElement) {\n                    console.log(\"\\uD83D\\uDD27 DOM状态检查:\", {\n                        contentEditable: rootElement.contentEditable,\n                        isContentEditable: rootElement.isContentEditable,\n                        tabIndex: rootElement.tabIndex,\n                        style: {\n                            pointerEvents: getComputedStyle(rootElement).pointerEvents,\n                            userSelect: getComputedStyle(rootElement).userSelect,\n                            cursor: getComputedStyle(rootElement).cursor,\n                            display: getComputedStyle(rootElement).display,\n                            visibility: getComputedStyle(rootElement).visibility\n                        },\n                        hasChildren: rootElement.children.length,\n                        textContent: rootElement.textContent,\n                        innerHTML: rootElement.innerHTML.substring(0, 200)\n                    });\n                    // 🔧 强制设置contentEditable\n                    if (rootElement.contentEditable !== \"true\" && !readOnly) {\n                        console.log(\"\\uD83D\\uDD27 强制设置contentEditable为true\");\n                        rootElement.contentEditable = \"true\";\n                    }\n                    // 🔧 强制设置tabIndex\n                    if (rootElement.tabIndex < 0) {\n                        console.log(\"\\uD83D\\uDD27 设置tabIndex为0\");\n                        rootElement.tabIndex = 0;\n                    }\n                    // 🔧 检查是否有全局事件监听器干扰\n                    var testInput = function() {\n                        console.log(\"\\uD83D\\uDD27 测试输入功能...\");\n                        rootElement.focus();\n                        // 模拟键盘输入\n                        var event = new KeyboardEvent(\"keydown\", {\n                            key: \"a\",\n                            code: \"KeyA\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        var inputEvent = new InputEvent(\"beforeinput\", {\n                            inputType: \"insertText\",\n                            data: \"a\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        console.log(\"\\uD83D\\uDD27 派发测试事件...\");\n                        var keyResult = rootElement.dispatchEvent(event);\n                        var inputResult = rootElement.dispatchEvent(inputEvent);\n                        console.log(\"\\uD83D\\uDD27 事件派发结果:\", {\n                            keyEvent: keyResult,\n                            inputEvent: inputResult,\n                            keyDefaultPrevented: event.defaultPrevented,\n                            inputDefaultPrevented: inputEvent.defaultPrevented\n                        });\n                    };\n                    // 延迟测试，确保编辑器完全初始化\n                    setTimeout(testInput, 1000);\n                }\n            };\n            // 立即检查\n            checkDOMState();\n            // 定期检查\n            var interval = setInterval(checkDOMState, 5000);\n            return function() {\n                return clearInterval(interval);\n            };\n        }, [\n            editor,\n            readOnly\n        ]);\n        return null;\n    };\n    _s4(DOMStatePlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false,\n                                style: {\n                                    userSelect: \"text\",\n                                    WebkitUserSelect: \"text\",\n                                    MozUserSelect: \"text\",\n                                    msUserSelect: \"text\",\n                                    cursor: \"text\",\n                                    minHeight: \"200px\"\n                                },\n                                onKeyDown: function(e) {\n                                    console.log(\"\\uD83C\\uDFB9 键盘按下:\", {\n                                        key: e.key,\n                                        code: e.code,\n                                        ctrlKey: e.ctrlKey,\n                                        shiftKey: e.shiftKey,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onFocus: function(e) {\n                                    console.log(\"\\uD83C\\uDFAF 编辑器获得焦点:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                },\n                                onBlur: function() {\n                                    console.log(\"\\uD83D\\uDE34 编辑器失去焦点\");\n                                },\n                                onClick: function(e) {\n                                    console.log(\"\\uD83D\\uDDB1️ 编辑器点击:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 701,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 756,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 768,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DOMStatePlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 769,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 700,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 699,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 698,\n        columnNumber: 9\n    }, _this);\n}, \"iuZ+b0O1CXZIV7Hez3f+DTYXPqg=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"iuZ+b0O1CXZIV7Hez3f+DTYXPqg=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});