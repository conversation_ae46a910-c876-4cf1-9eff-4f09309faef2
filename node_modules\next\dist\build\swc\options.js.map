{"version": 3, "sources": ["../../../build/swc/options.js"], "names": ["getParserOptions", "getJestSWCOptions", "getLoaderSWCOptions", "nextDistPath", "regeneratorRuntimePath", "require", "resolve", "filename", "jsConfig", "rest", "isTSFile", "endsWith", "isTypeScript", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "nextConfig", "resolvedBaseUrl", "swcCacheDir", "isServerLayer", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "experimental", "swcPlugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "keepImportAssertions", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "compiler", "emotion", "runtime", "pragma", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "styledComponents", "getStyledComponentsOptions", "removeConsole", "reactRemoveProperties", "modularizeImports", "relay", "getEmotionOptions", "serverComponents", "isServer", "styledComponentsOptions", "displayName", "autoLabel", "enabled", "labelFormat", "sourcemap", "sourceMap", "esm", "pagesDir", "baseOptions", "isNextDist", "test", "env", "targets", "node", "module", "type", "disableNextSsg", "disablePageConfig", "isPageFile", "supportedBrowsers", "isDevelopment", "target", "length"], "mappings": "AAAA;;;;QAOgBA,gBAAgB,GAAhBA,gBAAgB;QAgKhBC,iBAAiB,GAAjBA,iBAAiB;QAwCjBC,mBAAmB,GAAnBA,mBAAmB;AA/MnC,MAAMC,YAAY,4FACyE;AAE3F,MAAMC,sBAAsB,GAAGC,OAAO,CAACC,OAAO,CAC5C,wCAAwC,CACzC;AAEM,SAASN,gBAAgB,CAAC,EAAEO,QAAQ,CAAA,EAAEC,QAAQ,CAAA,EAAE,GAAGC,IAAI,EAAE,EAAE;QAI9DD,GAAyB;IAH3B,MAAME,QAAQ,GAAGH,QAAQ,CAACI,QAAQ,CAAC,KAAK,CAAC;IACzC,MAAMC,YAAY,GAAGF,QAAQ,IAAIH,QAAQ,CAACI,QAAQ,CAAC,MAAM,CAAC;IAC1D,MAAME,gBAAgB,GAAGC,OAAO,CAC9BN,QAAQ,QAAiB,GAAzBA,KAAAA,CAAyB,GAAzBA,CAAAA,GAAyB,GAAzBA,QAAQ,CAAEO,eAAe,SAAA,GAAzBP,KAAAA,CAAyB,GAAzBA,GAAyB,CAAEQ,sBAAsB,AAAxB,CAC1B;IACD,OAAO;QACL,GAAGP,IAAI;QACPQ,MAAM,EAAEL,YAAY,GAAG,YAAY,GAAG,YAAY;QAClDM,aAAa,EAAE,IAAI;QACnBC,UAAU,EAAEN,gBAAgB;QAC5B,qKAAqK;QACrK,CAACD,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,CAACF,QAAQ;QACzCU,gBAAgB,EAAE,IAAI;KACvB,CAAA;CACF;AAED,SAASC,iBAAiB,CAAC,EACzBd,QAAQ,CAAA,EACRe,IAAI,CAAA,EACJC,WAAW,CAAA,EACXC,eAAe,CAAA,EACfC,YAAY,CAAA,EACZC,UAAU,CAAA,EACVC,eAAe,CAAA,EACfnB,QAAQ,CAAA,EACRoB,WAAW,CAAA,EACXC,aAAa,CAAA,IACd,EAAE;QAEarB,GAAyB,EAErCA,IAAyB,EAGzBA,IAAyB,EAGzBA,IAAyB,EAEVkB,IAAwB,EAiC/BlB,IAAyB,EACxBkB,IAAoB,EA8BdA,IAAoB,EAK/BA,IAAoB,EACLA,IAAwB,EACpCA,KAAoB,EAETA,KAAwB;IApF5C,MAAMI,YAAY,GAAG9B,gBAAgB,CAAC;QAAEO,QAAQ;QAAEC,QAAQ;KAAE,CAAC;IAC7D,MAAMuB,KAAK,GAAGvB,QAAQ,QAAiB,GAAzBA,KAAAA,CAAyB,GAAzBA,CAAAA,GAAyB,GAAzBA,QAAQ,CAAEO,eAAe,SAAA,GAAzBP,KAAAA,CAAyB,GAAzBA,GAAyB,CAAEuB,KAAK,AAAP;IACvC,MAAMlB,gBAAgB,GAAGC,OAAO,CAC9BN,QAAQ,QAAiB,GAAzBA,KAAAA,CAAyB,GAAzBA,CAAAA,IAAyB,GAAzBA,QAAQ,CAAEO,eAAe,SAAA,GAAzBP,KAAAA,CAAyB,GAAzBA,IAAyB,CAAEQ,sBAAsB,AAAxB,CAC1B;IACD,MAAMgB,qBAAqB,GAAGlB,OAAO,CACnCN,QAAQ,QAAiB,GAAzBA,KAAAA,CAAyB,GAAzBA,CAAAA,IAAyB,GAAzBA,QAAQ,CAAEO,eAAe,SAAA,GAAzBP,KAAAA,CAAyB,GAAzBA,IAAyB,CAAEwB,qBAAqB,AAAvB,CAC1B;IACD,MAAMC,uBAAuB,GAAGnB,OAAO,CACrCN,QAAQ,QAAiB,GAAzBA,KAAAA,CAAyB,GAAzBA,CAAAA,IAAyB,GAAzBA,QAAQ,CAAEO,eAAe,SAAA,GAAzBP,KAAAA,CAAyB,GAAzBA,IAAyB,CAAEyB,uBAAuB,AAAzB,CAC1B;QACgBP,KAAoC;IAArD,MAAMQ,OAAO,GAAG,CAACR,CAAAA,KAAoC,GAApCA,UAAU,QAAc,GAAxBA,KAAAA,CAAwB,GAAxBA,CAAAA,IAAwB,GAAxBA,UAAU,CAAES,YAAY,SAAA,GAAxBT,KAAAA,CAAwB,GAAxBA,IAAwB,CAAEU,UAAU,AAAZ,YAAxBV,KAAoC,GAAI,EAAE,CAAC,CACzDW,MAAM,CAACC,KAAK,CAACC,OAAO,CAAC,CACrBC,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAK;YAACrC,OAAO,CAACC,OAAO,CAACmC,IAAI,CAAC;YAAEC,OAAO;SAAC,CAAC;QA+BnDlC,KAA0C;IA7BpD,OAAO;QACLmC,GAAG,EAAE;YACH,GAAIhB,eAAe,IAAII,KAAK,GACxB;gBACEa,OAAO,EAAEjB,eAAe;gBACxBI,KAAK;aACN,GACD,EAAE;YACNc,eAAe,EAAE,CAACC,OAAO,CAACC,QAAQ,CAACC,GAAG,IAAI,CAAC1B,IAAI;YAC/C2B,MAAM,EAAEnB,YAAY;YACpBK,YAAY,EAAE;gBACZe,oBAAoB,EAAE,IAAI;gBAC1BhB,OAAO;gBACPiB,SAAS,EAAEvB,WAAW;aACvB;YACDwB,SAAS,EAAE;gBACT,sIAAsI;gBACtI,GAAI9B,IAAI,GACJ;oBACE+B,MAAM,EAAE;wBACN/B,IAAI,EAAE,IAAI;qBACX;iBACF,GACD,EAAE;gBACNgC,eAAe,EAAEzC,gBAAgB;gBACjC0C,iBAAiB,EAAEvB,qBAAqB;gBACxCC,uBAAuB,EAAEA,uBAAuB;gBAChDuB,KAAK,EAAE;oBACLC,YAAY,EACVjD,CAAAA,KAA0C,GAA1CA,QAAQ,QAAiB,GAAzBA,KAAAA,CAAyB,GAAzBA,CAAAA,IAAyB,GAAzBA,QAAQ,CAAEO,eAAe,SAAA,GAAzBP,KAAAA,CAAyB,GAAzBA,IAAyB,CAAEkD,eAAe,AAAjB,YAAzBlD,KAA0C,GACzCkB,CAAAA,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,IAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,IAAoB,CAAEkC,OAAO,AAAT,CAAA,GAAY,gBAAgB,GAAG,OAAO,AAAC;oBAC9DC,OAAO,EAAE,WAAW;oBACpBC,MAAM,EAAE,qBAAqB;oBAC7BC,UAAU,EAAE,gBAAgB;oBAC5BC,gBAAgB,EAAE,IAAI;oBACtBzC,WAAW,EAAE,CAAC,CAACA,WAAW;oBAC1B0C,WAAW,EAAE,IAAI;oBACjBC,OAAO,EAAE,CAAC,CAAC1C,eAAe;iBAC3B;gBACD2C,SAAS,EAAE;oBACTC,QAAQ,EAAE,KAAK;oBACfC,OAAO,EAAE/C,IAAI,GACT,IAAI,GACJ;wBACEgD,OAAO,EAAE;4BACPC,MAAM,EAAE9C,YAAY,GAAG,QAAQ,GAAG,WAAW;yBAC9C;wBACD+C,IAAI,EAAE;4BACJC,QAAQ,EAAElD,WAAW,GAAG,eAAe,GAAG,cAAc;yBACzD;qBAEF;iBACN;gBACDmD,WAAW,EAAE;oBACXC,UAAU,EAAEvE,sBAAsB;iBACnC;aACF;SACF;QACDwE,UAAU,EAAEtD,IAAI,GAAG,QAAQ,GAAGuD,SAAS;QACvCC,gBAAgB,EAAEC,0BAA0B,CAACrD,UAAU,EAAEH,WAAW,CAAC;QACrEyD,aAAa,EAAEtD,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,IAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,IAAoB,CAAEsD,aAAa,AAAf;QACnC,sDAAsD;QACtD,yDAAyD;QACzDC,qBAAqB,EAAE3D,IAAI,GACvB,KAAK,GACLI,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,IAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,IAAoB,CAAEuD,qBAAqB,AAAvB;QACxBC,iBAAiB,EAAExD,UAAU,QAAc,GAAxBA,KAAAA,CAAwB,GAAxBA,CAAAA,IAAwB,GAAxBA,UAAU,CAAES,YAAY,SAAA,GAAxBT,KAAAA,CAAwB,GAAxBA,IAAwB,CAAEwD,iBAAiB,AAAnB;QAC3CC,KAAK,EAAEzD,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,KAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,KAAoB,CAAEyD,KAAK,AAAP;QAC3BvB,OAAO,EAAEwB,iBAAiB,CAAC1D,UAAU,EAAEH,WAAW,CAAC;QACnD8D,gBAAgB,EAAE3D,CAAAA,UAAU,QAAc,GAAxBA,KAAAA,CAAwB,GAAxBA,CAAAA,KAAwB,GAAxBA,UAAU,CAAES,YAAY,SAAA,GAAxBT,KAAAA,CAAwB,GAAxBA,KAAwB,CAAE2D,gBAAgB,AAAlB,CAAA,GACtC;YACEC,QAAQ,EAAE,CAAC,CAACzD,aAAa;SAC1B,GACD,KAAK;KACV,CAAA;CACF;AAED,SAASkD,0BAA0B,CAACrD,UAAU,EAAEH,WAAW,EAAE;QAC7BG,GAAoB;IAAlD,IAAI6D,uBAAuB,GAAG7D,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,GAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,GAAoB,CAAEoD,gBAAgB,AAAlB;IAClD,IAAI,CAACS,uBAAuB,EAAE;QAC5B,OAAO,IAAI,CAAA;KACZ;QAIcA,YAAmC;IAFlD,OAAO;QACL,GAAGA,uBAAuB;QAC1BC,WAAW,EAAED,CAAAA,YAAmC,GAAnCA,uBAAuB,CAACC,WAAW,YAAnCD,YAAmC,GAAIzE,OAAO,CAACS,WAAW,CAAC;KACzE,CAAA;CACF;AAED,SAAS6D,iBAAiB,CAAC1D,UAAU,EAAEH,WAAW,EAAE;QAC7CG,GAAoB,EAIjBA,KAAoB,SAebA,KAAoB,SAE7BA,KAAoB;IArB1B,IAAI,CAACA,CAAAA,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,GAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,GAAoB,CAAEkC,OAAO,AAAT,CAAA,AAAS,EAAE;QAClC,OAAO,IAAI,CAAA;KACZ;IACD,IAAI6B,SAAS,GAAG,KAAK;IACrB,OAAQ/D,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,KAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,SAAAA,KAAoB,CAAEkC,OAAO,SAAT,GAApBlC,KAAAA,CAAoB,SAAW+D,SAAS,AAApB;QAC1B,KAAK,OAAO;YACVA,SAAS,GAAG,KAAK;YACjB,MAAK;QACP,KAAK,QAAQ;YACXA,SAAS,GAAG,IAAI;YAChB,MAAK;QACP,KAAK,UAAU,CAAC;QAChB;YACEA,SAAS,GAAG,CAAC,CAAClE,WAAW;YACzB,MAAK;KACR;QAMKG,KAAwC;IAL9C,OAAO;QACLgE,OAAO,EAAE,IAAI;QACbD,SAAS;QACTE,WAAW,EAAEjE,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,KAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,SAAAA,KAAoB,CAAEkC,OAAO,SAAT,GAApBlC,KAAAA,CAAoB,SAAWiE,WAAW,AAAtB;QACjCC,SAAS,EAAErE,WAAW,GAClBG,CAAAA,KAAwC,GAAxCA,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,CAAAA,KAAoB,GAApBA,UAAU,CAAEiC,QAAQ,SAAA,GAApBjC,KAAAA,CAAoB,GAApBA,SAAAA,KAAoB,CAAEkC,OAAO,SAAT,GAApBlC,KAAAA,CAAoB,SAAWmE,SAAS,AAApB,YAApBnE,KAAwC,GAAI,IAAI,GAChD,KAAK;KACV,CAAA;CACF;AAEM,SAASzB,iBAAiB,CAAC,EAChCqF,QAAQ,CAAA,EACR/E,QAAQ,CAAA,EACRuF,GAAG,CAAA,EACHpE,UAAU,CAAA,EACVlB,QAAQ,CAAA,EACRuF,QAAQ,CAAA,IAGT,EAAE;IACD,IAAIC,WAAW,GAAG3E,iBAAiB,CAAC;QAClCd,QAAQ;QACRe,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,CAAC6D,QAAQ;QACvB5D,UAAU;QACVlB,QAAQ;KAET,CAAC;IAEF,MAAMyF,UAAU,GAAG9F,YAAY,CAAC+F,IAAI,CAAC3F,QAAQ,CAAC;IAE9C,OAAO;QACL,GAAGyF,WAAW;QACdG,GAAG,EAAE;YACHC,OAAO,EAAE;gBACP,yCAAyC;gBACzCC,IAAI,EAAEvD,OAAO,CAACC,QAAQ,CAACsD,IAAI;aAC5B;SACF;QACDC,MAAM,EAAE;YACNC,IAAI,EAAET,GAAG,IAAI,CAACG,UAAU,GAAG,KAAK,GAAG,UAAU;SAC9C;QACDO,cAAc,EAAE,IAAI;QACpBC,iBAAiB,EAAE,IAAI;QACvBV,QAAQ;KACT,CAAA;CACF;AAEM,SAAS7F,mBAAmB,CAAC,EAClCK,QAAQ,CAAA,EACRgB,WAAW,CAAA,EACX+D,QAAQ,CAAA,EACRzD,aAAa,CAAA,EACbkE,QAAQ,CAAA,EACRW,UAAU,CAAA,EACVlF,eAAe,CAAA,EACfE,UAAU,CAAA,EACVlB,QAAQ,CAAA,EACRmG,iBAAiB,CAAA,EACjB/E,WAAW,CAAA,IAGZ,EAAE;IACD,IAAIoE,WAAW,GAAG3E,iBAAiB,CAAC;QAClCd,QAAQ;QACRgB,WAAW;QACXE,YAAY,EAAE,CAAC6D,QAAQ;QACvB9D,eAAe;QACfE,UAAU;QACVlB,QAAQ;QACR,mBAAmB;QACnBoB,WAAW;QACXC,aAAa;KACd,CAAC;IAEF,MAAMoE,UAAU,GAAG9F,YAAY,CAAC+F,IAAI,CAAC3F,QAAQ,CAAC;IAE9C,IAAI+E,QAAQ,EAAE;QACZ,OAAO;YACL,GAAGU,WAAW;YACd,8FAA8F;YAC9FQ,cAAc,EAAE,IAAI;YACpBC,iBAAiB,EAAE,IAAI;YACvBG,aAAa,EAAErF,WAAW;YAC1B+D,QAAQ;YACRS,QAAQ;YACRW,UAAU;YACVP,GAAG,EAAE;gBACHC,OAAO,EAAE;oBACP,yCAAyC;oBACzCC,IAAI,EAAEvD,OAAO,CAACC,QAAQ,CAACsD,IAAI;iBAC5B;aACF;SACF,CAAA;KACF,MAAM;QACL,6CAA6C;QAC7CL,WAAW,CAACrD,GAAG,CAACkE,MAAM,GAAG,KAAK;QAC9B,OAAO;YACL,GAAGb,WAAW;YACd,0DAA0D;YAC1D,GAAIC,UAAU,GACV;gBACEK,MAAM,EAAE;oBACNC,IAAI,EAAE,UAAU;iBACjB;aACF,GACD,EAAE;YACNC,cAAc,EAAE,CAACE,UAAU;YAC3BE,aAAa,EAAErF,WAAW;YAC1B+D,QAAQ;YACRS,QAAQ;YACRW,UAAU;YACV,GAAIC,iBAAiB,IAAIA,iBAAiB,CAACG,MAAM,GAAG,CAAC,GACjD;gBACEX,GAAG,EAAE;oBACHC,OAAO,EAAEO,iBAAiB;iBAC3B;aACF,GACD,EAAE;SACP,CAAA;KACF;CACF"}