/**
 * 最小化的 Lexical 编辑器测试页面
 * 用于排除其他组件的干扰，专注测试编辑器输入功能
 */

import { useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { EditorState } from 'lexical';

const theme = {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
};

function Placeholder() {
    return <div className="editor-placeholder">开始输入...</div>;
}

export default function TestLexical() {
    const [editorState, setEditorState] = useState<string>('');

    const initialConfig = {
        namespace: 'TestLexicalEditor',
        theme,
        onError(error: Error) {
            console.error('Lexical Error:', error);
        },
        nodes: [
            HeadingNode,
            ListNode,
            ListItemNode,
            QuoteNode,
            CodeNode,
            CodeHighlightNode,
            AutoLinkNode,
            LinkNode,
        ],
        editable: true,
    };

    const handleChange = (editorState: EditorState) => {
        console.log('🔧 编辑器状态变化:', editorState);
        const json = JSON.stringify(editorState.toJSON());
        setEditorState(json);
        console.log('🔧 JSON内容:', json);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <h1>Lexical 编辑器测试</h1>
            <p>这是一个最小化的 Lexical 编辑器，用于测试输入功能。</p>
            
            <div style={{ 
                border: '1px solid #ccc', 
                borderRadius: '4px', 
                padding: '10px',
                minHeight: '200px',
                marginBottom: '20px'
            }}>
                <LexicalComposer initialConfig={initialConfig}>
                    <RichTextPlugin
                        contentEditable={
                            <ContentEditable
                                className="editor-input"
                                style={{
                                    outline: 'none',
                                    minHeight: '150px',
                                    padding: '10px',
                                    cursor: 'text',
                                    userSelect: 'text'
                                }}
                                spellCheck={false}
                                onKeyDown={(e) => {
                                    console.log('🎹 键盘事件:', e.key, e.code);
                                }}
                                onInput={(e) => {
                                    console.log('📝 输入事件:', (e as any).inputType, (e as any).data);
                                }}
                                onFocus={() => {
                                    console.log('🎯 获得焦点');
                                }}
                                onBlur={() => {
                                    console.log('😴 失去焦点');
                                }}
                            />
                        }
                        placeholder={<Placeholder />}
                        ErrorBoundary={LexicalErrorBoundary}
                    />
                    <HistoryPlugin />
                    <AutoFocusPlugin />
                    <OnChangePlugin onChange={handleChange} />
                </LexicalComposer>
            </div>

            <div>
                <h3>编辑器状态 (JSON):</h3>
                <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '10px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto',
                    maxHeight: '200px'
                }}>
                    {editorState || '(空)'}
                </pre>
            </div>

            <style jsx global>{`
                .editor-placeholder {
                    color: #999;
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    pointer-events: none;
                }
                
                .editor-paragraph {
                    margin: 0;
                    line-height: 1.5;
                }
            `}</style>
        </div>
    );
}
