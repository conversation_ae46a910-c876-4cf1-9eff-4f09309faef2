/**
 * Indent Plugin for Lexical
 * Provides indentation functionality for paragraphs and headings
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
    $getSelection,
    $isRangeSelection,
    COMMAND_PRIORITY_LOW,
    createCommand,
    KEY_TAB_COMMAND,
    LexicalCommand,
} from 'lexical';
import { $isHeadingNode } from '@lexical/rich-text';
import { $isParagraphNode } from 'lexical';
import { useEffect } from 'react';

export const INDENT_CONTENT_COMMAND: LexicalCommand<void> = createCommand(
    'INDENT_CONTENT_COMMAND',
);

export const OUTDENT_CONTENT_COMMAND: LexicalCommand<void> = createCommand(
    'OUTDENT_CONTENT_COMMAND',
);

interface IndentPluginProps {
    indentSize?: number;
    maxIndentLevel?: number;
}

export default function IndentPlugin({
    indentSize = 2,
    maxIndentLevel = 10,
}: IndentPluginProps = {}): null {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        const removeTabCommand = editor.registerCommand(
            KEY_TAB_COMMAND,
            (event: KeyboardEvent) => {
                const selection = $getSelection();
                if ($isRangeSelection(selection)) {
                    event.preventDefault();
                    
                    if (event.shiftKey) {
                        // Shift+Tab: Outdent
                        editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
                    } else {
                        // Tab: Indent
                        editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
                    }
                    return true;
                }
                return false;
            },
            COMMAND_PRIORITY_LOW,
        );

        const removeIndentCommand = editor.registerCommand(
            INDENT_CONTENT_COMMAND,
            () => {
                const selection = $getSelection();
                if ($isRangeSelection(selection)) {
                    const nodes = selection.getNodes();
                    
                    for (const node of nodes) {
                        const element = node.getTopLevelElement();
                        if (element && ($isParagraphNode(element) || $isHeadingNode(element))) {
                            const currentIndent = element.getIndent();
                            if (currentIndent < maxIndentLevel) {
                                element.setIndent(currentIndent + 1);
                            }
                        }
                    }
                }
                return true;
            },
            COMMAND_PRIORITY_LOW,
        );

        const removeOutdentCommand = editor.registerCommand(
            OUTDENT_CONTENT_COMMAND,
            () => {
                const selection = $getSelection();
                if ($isRangeSelection(selection)) {
                    const nodes = selection.getNodes();
                    
                    for (const node of nodes) {
                        const element = node.getTopLevelElement();
                        if (element && ($isParagraphNode(element) || $isHeadingNode(element))) {
                            const currentIndent = element.getIndent();
                            if (currentIndent > 0) {
                                element.setIndent(currentIndent - 1);
                            }
                        }
                    }
                }
                return true;
            },
            COMMAND_PRIORITY_LOW,
        );

        return () => {
            removeTabCommand();
            removeIndentCommand();
            removeOutdentCommand();
        };
    }, [editor, indentSize, maxIndentLevel]);

    return null;
}
