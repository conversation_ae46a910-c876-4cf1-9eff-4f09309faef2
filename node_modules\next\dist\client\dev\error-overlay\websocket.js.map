{"version": 3, "sources": ["../../../../client/dev/error-overlay/websocket.ts"], "names": ["addMessageListener", "sendMessage", "connectHMR", "source", "eventCallbacks", "lastActivity", "Date", "now", "getSocketProtocol", "assetPrefix", "protocol", "location", "URL", "_", "cb", "push", "data", "readyState", "OPEN", "send", "options", "timeout", "init", "close", "handleOnline", "log", "console", "handleMessage", "event", "for<PERSON>ach", "timer", "handleDisconnect", "clearInterval", "setTimeout", "setInterval", "hostname", "port", "replace", "url", "startsWith", "split", "window", "WebSocket", "path", "onopen", "onerror", "onmessage"], "mappings": "AAAA;;;;QAegBA,kBAAkB,GAAlBA,kBAAkB;QAIlBC,WAAW,GAAXA,WAAW;QAKXC,UAAU,GAAVA,UAAU;AAxB1B,IAAIC,MAAM,AAAW;AACrB,MAAMC,cAAc,GAA6B,EAAE;AACnD,IAAIC,YAAY,GAAGC,IAAI,CAACC,GAAG,EAAE;AAE7B,SAASC,iBAAiB,CAACC,WAAmB,EAAU;IACtD,IAAIC,QAAQ,GAAGC,QAAQ,CAACD,QAAQ;IAEhC,IAAI;QACF,uBAAuB;QACvBA,QAAQ,GAAG,IAAIE,GAAG,CAACH,WAAW,CAAC,CAACC,QAAQ;KACzC,CAAC,OAAOG,CAAC,EAAE,EAAE;IAEd,OAAOH,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAG,KAAK,CAAA;CAC3C;AAEM,SAASV,kBAAkB,CAACc,EAAwB,EAAE;IAC3DV,cAAc,CAACW,IAAI,CAACD,EAAE,CAAC;CACxB;AAEM,SAASb,WAAW,CAACe,IAAS,EAAE;IACrC,IAAI,CAACb,MAAM,IAAIA,MAAM,CAACc,UAAU,KAAKd,MAAM,CAACe,IAAI,EAAE,OAAM;IACxD,OAAOf,MAAM,CAACgB,IAAI,CAACH,IAAI,CAAC,CAAA;CACzB;AAEM,SAASd,UAAU,CAACkB,OAK1B,EAAE;IACD,IAAI,CAACA,OAAO,CAACC,OAAO,EAAE;QACpBD,OAAO,CAACC,OAAO,GAAG,CAAC,GAAG,IAAI;KAC3B;IAED,SAASC,IAAI,GAAG;QACd,IAAInB,MAAM,EAAEA,MAAM,CAACoB,KAAK,EAAE;QAE1B,SAASC,YAAY,GAAG;YACtB,IAAIJ,OAAO,CAACK,GAAG,EAAEC,OAAO,CAACD,GAAG,CAAC,iBAAiB,CAAC;YAC/CpB,YAAY,GAAGC,IAAI,CAACC,GAAG,EAAE;SAC1B;QAED,SAASoB,aAAa,CAACC,KAAU,EAAE;YACjCvB,YAAY,GAAGC,IAAI,CAACC,GAAG,EAAE;YAEzBH,cAAc,CAACyB,OAAO,CAAC,CAACf,EAAE,GAAK;gBAC7BA,EAAE,CAACc,KAAK,CAAC;aACV,CAAC;SACH;QAED,IAAIE,KAAK,AAAgB;QACzB,SAASC,gBAAgB,GAAG;YAC1BC,aAAa,CAACF,KAAK,CAAC;YACpB3B,MAAM,CAACoB,KAAK,EAAE;YACdU,UAAU,CAACX,IAAI,EAAEF,OAAO,CAACC,OAAO,CAAC;SAClC;QACDS,KAAK,GAAGI,WAAW,CAAC,WAAY;YAC9B,IAAI5B,IAAI,CAACC,GAAG,EAAE,GAAGF,YAAY,GAAGe,OAAO,CAACC,OAAO,EAAE;gBAC/CU,gBAAgB,EAAE;aACnB;SACF,EAAEX,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC;QAEvB,MAAM,EAAEc,QAAQ,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAGzB,QAAQ;QACnC,MAAMD,QAAQ,GAAGF,iBAAiB,CAACY,OAAO,CAACX,WAAW,IAAI,EAAE,CAAC;QAC7D,MAAMA,WAAW,GAAGW,OAAO,CAACX,WAAW,CAAC4B,OAAO,SAAS,EAAE,CAAC;QAE3D,IAAIC,GAAG,GAAG,CAAC,EAAE5B,QAAQ,CAAC,GAAG,EAAEyB,QAAQ,CAAC,CAAC,EAAEC,IAAI,CAAC,EAC1C3B,WAAW,GAAG,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,GAAG,EAAE,CACrC,CAAC;QAEF,IAAIA,WAAW,CAAC8B,UAAU,CAAC,MAAM,CAAC,EAAE;YAClCD,GAAG,GAAG,CAAC,EAAE5B,QAAQ,CAAC,GAAG,EAAED,WAAW,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACrD;QAEDrC,MAAM,GAAG,IAAIsC,MAAM,CAACC,SAAS,CAAC,CAAC,EAAEJ,GAAG,CAAC,EAAElB,OAAO,CAACuB,IAAI,CAAC,CAAC,CAAC;QACtDxC,MAAM,CAACyC,MAAM,GAAGpB,YAAY;QAC5BrB,MAAM,CAAC0C,OAAO,GAAGd,gBAAgB;QACjC5B,MAAM,CAAC2C,SAAS,GAAGnB,aAAa;KACjC;IAEDL,IAAI,EAAE;CACP"}