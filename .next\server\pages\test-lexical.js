"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/test-lexical";
exports.ids = ["pages/test-lexical"];
exports.modules = {

/***/ "./pages/test-lexical.tsx":
/*!********************************!*\
  !*** ./pages/test-lexical.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestLexical)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"@lexical/react/LexicalComposer\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"@lexical/react/LexicalRichTextPlugin\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"@lexical/react/LexicalContentEditable\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"@lexical/react/LexicalHistoryPlugin\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"@lexical/react/LexicalAutoFocusPlugin\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"@lexical/react/LexicalOnChangePlugin\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"@lexical/react/LexicalErrorBoundary\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/rich-text */ \"@lexical/rich-text\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @lexical/list */ \"@lexical/list\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_lexical_list__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @lexical/code */ \"@lexical/code\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_lexical_code__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/link */ \"@lexical/link\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_lexical_link__WEBPACK_IMPORTED_MODULE_13__);\n/**\n * 最小化的 Lexical 编辑器测试页面\n * 用于排除其他组件的干扰，专注测试编辑器输入功能\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nconst theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\"\n};\nfunction Placeholder() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: \"开始输入...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n        lineNumber: 28,\n        columnNumber: 12\n    }, this);\n}\nfunction TestLexical() {\n    const { 0: editorState , 1: setEditorState  } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const initialConfig = {\n        namespace: \"TestLexicalEditor\",\n        theme,\n        onError (error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_11__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_11__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_12__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_12__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_13__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_13__.LinkNode, \n        ],\n        editable: true\n    };\n    const handleChange = (editorState)=>{\n        console.log(\"\\uD83D\\uDD27 编辑器状态变化:\", editorState);\n        const json = JSON.stringify(editorState.toJSON());\n        setEditorState(json);\n        console.log(\"\\uD83D\\uDD27 JSON内容:\", json);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        className: \"jsx-4f6579cb680994d8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"jsx-4f6579cb680994d8\",\n                children: \"Lexical 编辑器测试\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"jsx-4f6579cb680994d8\",\n                children: \"这是一个最小化的 Lexical 编辑器，用于测试输入功能。\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    border: \"1px solid #ccc\",\n                    borderRadius: \"4px\",\n                    padding: \"10px\",\n                    minHeight: \"200px\",\n                    marginBottom: \"20px\"\n                },\n                className: \"jsx-4f6579cb680994d8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_3__.LexicalComposer, {\n                    initialConfig: initialConfig,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_4__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_5__.ContentEditable, {\n                                className: \"editor-input\",\n                                style: {\n                                    outline: \"none\",\n                                    minHeight: \"150px\",\n                                    padding: \"10px\",\n                                    cursor: \"text\",\n                                    userSelect: \"text\"\n                                },\n                                spellCheck: false,\n                                onKeyDown: (e)=>{\n                                    console.log(\"\\uD83C\\uDFB9 键盘事件:\", e.key, e.code);\n                                },\n                                onInput: (e)=>{\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", e.inputType, e.data);\n                                },\n                                onFocus: ()=>{\n                                    console.log(\"\\uD83C\\uDFAF 获得焦点\");\n                                },\n                                onBlur: ()=>{\n                                    console.log(\"\\uD83D\\uDE34 失去焦点\");\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_9__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_6__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_7__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_8__.OnChangePlugin, {\n                            onChange: handleChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-4f6579cb680994d8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"jsx-4f6579cb680994d8\",\n                        children: \"编辑器状态 (JSON):\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        style: {\n                            background: \"#f5f5f5\",\n                            padding: \"10px\",\n                            borderRadius: \"4px\",\n                            fontSize: \"12px\",\n                            overflow: \"auto\",\n                            maxHeight: \"200px\"\n                        },\n                        className: \"jsx-4f6579cb680994d8\",\n                        children: editorState || \"(空)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n                lineNumber: 108,\n                columnNumber: 13\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"4f6579cb680994d8\",\n                children: \".editor-placeholder{color:#999;position:absolute;top:10px;left:10px;pointer-events:none}.editor-paragraph{margin:0;line-height:1.5}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\test-lexical.tsx\",\n        lineNumber: 61,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/test-lexical.tsx\n");

/***/ }),

/***/ "@lexical/code":
/*!********************************!*\
  !*** external "@lexical/code" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@lexical/code");

/***/ }),

/***/ "@lexical/link":
/*!********************************!*\
  !*** external "@lexical/link" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@lexical/link");

/***/ }),

/***/ "@lexical/list":
/*!********************************!*\
  !*** external "@lexical/list" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@lexical/list");

/***/ }),

/***/ "@lexical/react/LexicalAutoFocusPlugin":
/*!********************************************************!*\
  !*** external "@lexical/react/LexicalAutoFocusPlugin" ***!
  \********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalAutoFocusPlugin");

/***/ }),

/***/ "@lexical/react/LexicalComposer":
/*!*************************************************!*\
  !*** external "@lexical/react/LexicalComposer" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalComposer");

/***/ }),

/***/ "@lexical/react/LexicalContentEditable":
/*!********************************************************!*\
  !*** external "@lexical/react/LexicalContentEditable" ***!
  \********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalContentEditable");

/***/ }),

/***/ "@lexical/react/LexicalErrorBoundary":
/*!******************************************************!*\
  !*** external "@lexical/react/LexicalErrorBoundary" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalErrorBoundary");

/***/ }),

/***/ "@lexical/react/LexicalHistoryPlugin":
/*!******************************************************!*\
  !*** external "@lexical/react/LexicalHistoryPlugin" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalHistoryPlugin");

/***/ }),

/***/ "@lexical/react/LexicalOnChangePlugin":
/*!*******************************************************!*\
  !*** external "@lexical/react/LexicalOnChangePlugin" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalOnChangePlugin");

/***/ }),

/***/ "@lexical/react/LexicalRichTextPlugin":
/*!*******************************************************!*\
  !*** external "@lexical/react/LexicalRichTextPlugin" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalRichTextPlugin");

/***/ }),

/***/ "@lexical/rich-text":
/*!*************************************!*\
  !*** external "@lexical/rich-text" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@lexical/rich-text");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/test-lexical.tsx"));
module.exports = __webpack_exports__;

})();