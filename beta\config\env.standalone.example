# motea Environment Configuration - Standalone Docker with Embedded PostgreSQL
# Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>
# Modified and maintained by waycaan, 2025.

# ===========================================
# Application Configuration
# ===========================================
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0

# ===========================================
# Database Configuration - Embedded PostgreSQL
# ===========================================
# The database is embedded in the Docker container
# No external database setup required!

# Database connection (automatically configured)
DATABASE_URL=postgresql://motea:motea@localhost:5432/motea

# Database provider (automatically set)
DB_PROVIDER=self-hosted

# Database table prefix (OPTIONAL)
# STORE_PREFIX=motea_

# PostgreSQL data directory (automatically configured)
PGDATA=/data

# ===========================================
# Authentication Configuration
# ===========================================
# Option 1: Set a password for the application (RECOMMENDED)
PASSWORD=your_secure_password_here

# Option 2: Disable password authentication (uncomment below)
# DISABLE_PASSWORD=true

# ===========================================
# Performance Configuration
# ===========================================
# Number of notes to preload (recommended: 5-15 for standalone)
PRELOAD_NOTES_COUNT=10

# Logging level
LOG_LEVEL=info

# ===========================================
# Session Configuration
# ===========================================
# Session secret for secure cookies
SESSION_SECRET=your_session_secret_here

# Use secure cookies in production (set to false for local development)
COOKIE_SECURE=false

# ===========================================
# Optional Configuration
# ===========================================
# Base URL for the application
# BASE_URL=https://your-domain.com

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# Docker Build Configuration (Optional)
# ===========================================
# These are used during Docker build process
# BUILDTIME=2025-01-01T00:00:00Z
# VERSION=latest
# REVISION=main

# ===========================================
# Standalone Docker Deployment Guide
# ===========================================
# 1. Copy this file to .env.local:
#    cp config/env.standalone.example .env.local
#
# 2. Edit .env.local and set your PASSWORD
#
# 3. Deploy using Docker Compose:
#    docker-compose -f docker-compose.standalone.yml up -d
#
# 4. Access your application:
#    http://localhost:3000
#
# 5. Optional: Access PostgreSQL directly:
#    postgresql://motea:motea@localhost:5432/motea

# ===========================================
# Standalone Features
# ===========================================
# ✅ Single Docker container
# ✅ Embedded PostgreSQL database
# ✅ Automatic database initialization
# ✅ Data persistence via Docker volumes
# ✅ No external dependencies
# ✅ Easy backup and restore
# ✅ Suitable for small to medium deployments

# ===========================================
# Resource Requirements
# ===========================================
# Minimum:
# - RAM: 256MB
# - CPU: 0.25 cores
# - Disk: 1GB
#
# Recommended:
# - RAM: 512MB
# - CPU: 0.5 cores
# - Disk: 5GB

# ===========================================
# Backup and Restore
# ===========================================
# Backup database:
# docker exec motea-standalone su-exec postgres pg_dump -U motea motea > backup.sql
#
# Restore database:
# docker exec -i motea-standalone su-exec postgres psql -U motea motea < backup.sql
#
# Backup data volume:
# docker run --rm -v motea_data:/data -v $(pwd):/backup alpine tar czf /backup/motea-data.tar.gz -C /data .
#
# Restore data volume:
# docker run --rm -v motea_data:/data -v $(pwd):/backup alpine tar xzf /backup/motea-data.tar.gz -C /data

# ===========================================
# Troubleshooting
# ===========================================
# View logs:
# docker-compose -f docker-compose.standalone.yml logs -f
#
# Access container shell:
# docker exec -it motea-standalone /bin/bash
#
# Check PostgreSQL status:
# docker exec motea-standalone su-exec postgres pg_isready
#
# Connect to database:
# docker exec -it motea-standalone su-exec postgres psql -U motea motea

# ===========================================
# Security Considerations
# ===========================================
# 1. Change default password immediately
# 2. Use strong SESSION_SECRET
# 3. Enable COOKIE_SECURE in production
# 4. Consider using reverse proxy with SSL
# 5. Regular backups
# 6. Monitor container logs
# 7. Keep Docker image updated

# ===========================================
# Production Deployment Tips
# ===========================================
# 1. Use Docker secrets for sensitive data
# 2. Set up log rotation
# 3. Monitor resource usage
# 4. Configure automatic restarts
# 5. Set up health checks
# 6. Use specific version tags instead of 'latest'
# 7. Consider using Docker Swarm or Kubernetes for scaling
