{"version": 3, "sources": ["../../../server/dev/static-paths-worker.ts"], "names": ["loadStaticPaths", "workerWasUsed", "distDir", "pathname", "serverless", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "originalAppPath", "process", "exit", "require", "setConfig", "setHttpAgentOptions", "components", "loadComponents", "hasServerComponents", "getStaticPaths", "Error", "generateParams", "collectGenerateParams", "ComponentMod", "tree", "buildAppStaticPaths", "page", "configFileName", "buildStaticPaths"], "mappings": "AAAA;;;;QAkBsBA,eAAe,GAAfA,eAAe;QAhB9B,wBAAwB;AAKxB,IAAA,MAAmB,WAAnB,mBAAmB,CAAA;AACK,IAAA,eAAoB,WAApB,oBAAoB,CAAA;AACf,IAAA,OAAW,WAAX,WAAW,CAAA;AAI/C,IAAIC,aAAa,GAAG,KAAK;AAKlB,eAAeD,eAAe,CAAC,EACpCE,OAAO,CAAA,EACPC,QAAQ,CAAA,EACRC,UAAU,CAAA,EACVC,MAAM,CAAA,EACNC,gBAAgB,CAAA,EAChBC,OAAO,CAAA,EACPC,aAAa,CAAA,EACbC,SAAS,CAAA,EACTC,eAAe,CAAA,EAWhB,EAIE;IACD,8DAA8D;IAC9D,SAAS;IACT,IAAIT,aAAa,EAAE;QACjBU,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;KAChB;IAED,oCAAoC;IACpCC,OAAO,CAAC,iCAAiC,CAAC,CAACC,SAAS,CAACT,MAAM,CAAC;IAC5DU,CAAAA,GAAAA,OAAmB,AAAkB,CAAA,oBAAlB,CAACT,gBAAgB,CAAC;IAErC,MAAMU,UAAU,GAAG,MAAMC,CAAAA,GAAAA,eAAc,AAMrC,CAAA,eANqC,CAAC;QACtCf,OAAO;QACPC,QAAQ,EAAEO,eAAe,IAAIP,QAAQ;QACrCC,UAAU;QACVc,mBAAmB,EAAE,KAAK;QAC1BT,SAAS,EAAE,CAAC,CAACA,SAAS;KACvB,CAAC;IAEF,IAAI,CAACO,UAAU,CAACG,cAAc,IAAI,CAACV,SAAS,EAAE;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIW,KAAK,CACb,CAAC,uDAAuD,EAAEjB,QAAQ,CAAC,CAAC,CACrE,CAAA;KACF;IACDF,aAAa,GAAG,IAAI;IAEpB,IAAIQ,SAAS,EAAE;QACb,MAAMY,cAAc,GAAGC,CAAAA,GAAAA,MAAqB,AAA8B,CAAA,sBAA9B,CAACN,UAAU,CAACO,YAAY,CAACC,IAAI,CAAC;QAC1E,OAAOC,CAAAA,GAAAA,MAAmB,AAIxB,CAAA,oBAJwB,CAAC;YACzBC,IAAI,EAAEvB,QAAQ;YACdkB,cAAc;YACdM,cAAc,EAAEtB,MAAM,CAACsB,cAAc;SACtC,CAAC,CAAA;KACH;IAED,OAAOC,CAAAA,GAAAA,MAAgB,AAMrB,CAAA,iBANqB,CAAC;QACtBF,IAAI,EAAEvB,QAAQ;QACdgB,cAAc,EAAEH,UAAU,CAACG,cAAc;QACzCQ,cAAc,EAAEtB,MAAM,CAACsB,cAAc;QACrCpB,OAAO;QACPC,aAAa;KACd,CAAC,CAAA;CACH"}