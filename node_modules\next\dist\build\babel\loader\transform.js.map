{"version": 3, "sources": ["../../../../build/babel/loader/transform.ts"], "names": ["transform", "source", "inputSourceMap", "loaderOptions", "filename", "target", "parentSpan", "getConfigSpan", "<PERSON><PERSON><PERSON><PERSON>", "babelConfig", "getConfig", "call", "stop", "normalizeSpan", "file", "consumeIterator", "normalizeFile", "passes", "normalizeOpts", "transformSpan", "transformAst", "generateSpan", "code", "map", "generate", "ast", "opts", "generatorOpts", "getTraversalParams", "pluginPairs", "passPairs", "visitors", "plugin", "concat", "loadBlockHoistPlugin", "pass", "Plug<PERSON><PERSON><PERSON>", "key", "options", "push", "visitor", "invokePluginPre", "pre", "invokePluginPost", "post", "transformAstPass", "traverse", "merge", "wrapPluginVisitorMethod", "traceFn", "scope"], "mappings": "AAIA;;;;kBAmEwBA,SAAS;AAnEZ,IAAA,SAAmC,kCAAnC,mCAAmC,EAAA;AACnC,IAAA,UAAoC,kCAApC,oCAAoC,EAAA;AAC/B,IAAA,qBAAkD,kCAAlD,kDAAkD,EAAA;AAClD,IAAA,qBAAkD,kCAAlD,kDAAkD,EAAA;AAC3C,IAAA,wBAAsD,kCAAtD,sDAAsD,EAAA;AAChE,IAAA,kBAA+C,kCAA/C,+CAA+C,EAAA;AAEhD,IAAA,UAAc,kCAAd,cAAc,EAAA;AACJ,IAAA,KAAQ,WAAR,QAAQ,CAAA;AA2DzB,SAASA,SAAS,CAE/BC,MAAc,EACdC,cAAyC,EACzCC,aAAkB,EAClBC,QAAgB,EAChBC,MAAc,EACdC,UAAgB,EAChB;IACA,MAAMC,aAAa,GAAGD,UAAU,CAACE,UAAU,CAAC,wBAAwB,CAAC;IACrE,MAAMC,WAAW,GAAGC,UAAS,QAAA,CAACC,IAAI,CAAC,IAAI,EAAE;QACvCV,MAAM;QACNE,aAAa;QACbD,cAAc;QACdG,MAAM;QACND,QAAQ;KACT,CAAC;IACFG,aAAa,CAACK,IAAI,EAAE;IAEpB,MAAMC,aAAa,GAAGP,UAAU,CAACE,UAAU,CAAC,4BAA4B,CAAC;IACzE,MAAMM,IAAI,GAAGC,CAAAA,GAAAA,KAAe,AAE3B,CAAA,gBAF2B,CAC1BC,CAAAA,GAAAA,qBAAa,AAAwD,CAAA,QAAxD,CAACP,WAAW,CAACQ,MAAM,EAAEC,CAAAA,GAAAA,qBAAa,AAAa,CAAA,QAAb,CAACT,WAAW,CAAC,EAAER,MAAM,CAAC,CACtE;IACDY,aAAa,CAACD,IAAI,EAAE;IAEpB,MAAMO,aAAa,GAAGb,UAAU,CAACE,UAAU,CAAC,uBAAuB,CAAC;IACpEY,YAAY,CAACN,IAAI,EAAEL,WAAW,EAAEU,aAAa,CAAC;IAC9CA,aAAa,CAACP,IAAI,EAAE;IAEpB,MAAMS,YAAY,GAAGf,UAAU,CAACE,UAAU,CAAC,sBAAsB,CAAC;IAClE,MAAM,EAAEc,IAAI,CAAA,EAAEC,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,UAAQ,AAA8C,CAAA,QAA9C,CAACV,IAAI,CAACW,GAAG,EAAEX,IAAI,CAACY,IAAI,CAACC,aAAa,EAAEb,IAAI,CAACQ,IAAI,CAAC;IAC5ED,YAAY,CAACT,IAAI,EAAE;IAEnB,OAAO;QAAEU,IAAI;QAAEC,GAAG;KAAE,CAAA;CACrB;;;;;;AAzFD,SAASK,kBAAkB,CAACd,IAAS,EAAEe,WAAkB,EAAE;IACzD,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAMb,MAAM,GAAG,EAAE;IACjB,MAAMc,QAAQ,GAAG,EAAE;IAEnB,KAAK,MAAMC,MAAM,IAAIH,WAAW,CAACI,MAAM,CAACC,CAAAA,GAAAA,wBAAoB,AAAE,CAAA,QAAF,EAAE,CAAC,CAAE;QAC/D,MAAMC,IAAI,GAAG,IAAIC,kBAAU,QAAA,CAACtB,IAAI,EAAEkB,MAAM,CAACK,GAAG,EAAEL,MAAM,CAACM,OAAO,CAAC;QAC7DR,SAAS,CAACS,IAAI,CAAC;YAACP,MAAM;YAAEG,IAAI;SAAC,CAAC;QAC9BlB,MAAM,CAACsB,IAAI,CAACJ,IAAI,CAAC;QACjBJ,QAAQ,CAACQ,IAAI,CAACP,MAAM,CAACQ,OAAO,CAAC;KAC9B;IAED,OAAO;QAAEV,SAAS;QAAEb,MAAM;QAAEc,QAAQ;KAAE,CAAA;CACvC;AAED,SAASU,eAAe,CAAC3B,IAAS,EAAEgB,SAAgB,EAAE;IACpD,KAAK,MAAM,CAAC,EAAEY,GAAG,CAAA,EAAE,EAAEP,IAAI,CAAC,IAAIL,SAAS,CAAE;QACvC,IAAIY,GAAG,EAAE;YACPA,GAAG,CAAC/B,IAAI,CAACwB,IAAI,EAAErB,IAAI,CAAC;SACrB;KACF;CACF;AAED,SAAS6B,gBAAgB,CAAC7B,IAAS,EAAEgB,SAAgB,EAAE;IACrD,KAAK,MAAM,CAAC,EAAEc,IAAI,CAAA,EAAE,EAAET,IAAI,CAAC,IAAIL,SAAS,CAAE;QACxC,IAAIc,IAAI,EAAE;YACRA,IAAI,CAACjC,IAAI,CAACwB,IAAI,EAAErB,IAAI,CAAC;SACtB;KACF;CACF;AAED,SAAS+B,gBAAgB,CAAC/B,IAAS,EAAEe,WAAkB,EAAEvB,UAAgB,EAAE;IACzE,MAAM,EAAEwB,SAAS,CAAA,EAAEb,MAAM,CAAA,EAAEc,QAAQ,CAAA,EAAE,GAAGH,kBAAkB,CAACd,IAAI,EAAEe,WAAW,CAAC;IAE7EY,eAAe,CAAC3B,IAAI,EAAEgB,SAAS,CAAC;IAChC,MAAMU,OAAO,GAAGM,SAAQ,QAAA,CAACf,QAAQ,CAACgB,KAAK,CACrChB,QAAQ,EACRd,MAAM,EACN,qDAAqD;IACrDH,IAAI,CAACY,IAAI,CAACsB,uBAAuB,CAClC;IAED1C,UAAU,CACPE,UAAU,CAAC,sBAAsB,CAAC,CAClCyC,OAAO,CAAC,IAAMH,CAAAA,GAAAA,SAAQ,AAA+B,CAAA,QAA/B,CAAChC,IAAI,CAACW,GAAG,EAAEe,OAAO,EAAE1B,IAAI,CAACoC,KAAK,CAAC,CAAC;IAEzDP,gBAAgB,CAAC7B,IAAI,EAAEgB,SAAS,CAAC;CAClC;AAED,SAASV,YAAY,CAACN,IAAS,EAAEL,WAAgB,EAAEH,UAAgB,EAAE;IACnE,KAAK,MAAMuB,WAAW,IAAIpB,WAAW,CAACQ,MAAM,CAAE;QAC5C4B,gBAAgB,CAAC/B,IAAI,EAAEe,WAAW,EAAEvB,UAAU,CAAC;KAChD;CACF"}