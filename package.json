{"name": "notea", "version": "0.4.0-alpha", "private": true, "packageManager": "npm@9.6.7", "scripts": {"dev": "next", "debug": "NODE_OPTIONS='--inspect' next dev", "build": "next build", "start": "next start", "type-check": "tsc --pretty --noEmit", "format": "prettier --write .", "lint": "eslint . --ext ts --ext tsx --ext js", "build:i18n": "node scripts/extract-i18n", "test": "jest"}, "repository": {"type": "git", "url": "https://github.com/notea-org/notea"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "run-s type-check"}}, "lint-staged": {"*.@(ts|tsx)": ["run-s lint format"]}, "dependencies": {"@atlaskit/tree": "^8.6.3", "@fontsource/noto-sans": "^4.5.10", "@heroicons/react": "^1.0.1", "@lexical/clipboard": "^0.32.1", "@lexical/code": "^0.32.1", "@lexical/hashtag": "^0.32.1", "@lexical/headless": "^0.32.1", "@lexical/history": "^0.32.1", "@lexical/html": "^0.32.1", "@lexical/link": "^0.32.1", "@lexical/list": "^0.32.1", "@lexical/markdown": "^0.32.1", "@lexical/overflow": "^0.32.1", "@lexical/plain-text": "^0.32.1", "@lexical/react": "^0.32.1", "@lexical/rich-text": "^0.32.1", "@lexical/selection": "^0.32.1", "@lexical/table": "^0.32.1", "@lexical/utils": "^0.32.1", "@material-ui/core": "^4.12.3", "@material-ui/lab": "^4.0.0-alpha.60", "@notea/headway-widget": "^0.0.5-alpha", "adm-zip": "^0.5.5", "classnames": "^2.2.6", "csrf": "^3.1.0", "dangerously-set-html-content": "^1.0.8", "dayjs": "^1.10.4", "emoji-regex": "^10.0.0", "escape-string-regexp": "^4.0.0", "express-rate-limit": "^7.5.1", "formidable": "^3.5.1", "highlight.js": "^10.7.2", "js-yaml": "^4.1.0", "lexical": "^0.32.1", "localforage": "^1.9.0", "lodash": "^4.17.21", "lzutf8": "^0.6.0", "markdown-it": "^14.1.0", "markdown-it-task-lists": "^2.1.1", "md5": "^2.3.0", "nanoid": "^3.1.22", "next": "^12.3.4", "next-connect": "^0.10.1", "next-iron-session": "^4.1.11", "next-pwa": "^5.6.0", "next-seo": "^4.24.0", "next-themes": "^0.0.14", "notistack": "^1.0.7", "outline-icons": "^1.27.0", "pg": "^8.8.0", "pino": "^8.7.0", "pino-pretty": "^9.1.1", "pupa": "^2.1.1", "qss": "^2.0.3", "react": "^17.0.2", "react-div-100vh": "^0.5.6", "react-dom": "^17.0.2", "react-resize-detector": "^6.6.0", "react-split": "^2.0.9", "refractor": "^4.8.1", "remove-markdown": "^0.3.0", "rosetta": "^1.1.0", "styled-components": "^5.2.1", "tippy.js": "^6.3.7", "typescript": "^4.3.4", "ua-parser-js": "^0.7.24", "unfurl.js": "^5.3.0", "unstated-next": "^1.1.0", "use-debounce": "^5.2.1"}, "devDependencies": {"@tailwindcss/typography": "^0.4.0", "@types/adm-zip": "^0.4.34", "@types/classnames": "^2.2.11", "@types/formidable": "^1.0.32", "@types/jest": "^27.0.1", "@types/js-yaml": "^4.0.5", "@types/lodash": "^4.14.168", "@types/markdown-it": "^14.1.2", "@types/md5": "^2.3.0", "@types/node": "^12.12.21", "@types/pg": "^8.6.5", "@types/react": "^17.0.50", "@types/react-dom": "^17.0.17", "@types/supertest": "^2.0.11", "@types/ua-parser-js": "^0.7.35", "@typescript-eslint/eslint-plugin": "^4.15.0", "@typescript-eslint/parser": "^4.15.0", "autoprefixer": "^10.2.4", "babel-jest": "^27.1.0", "babel-plugin-lodash": "^3.3.4", "dotenv": "^10.0.0", "eslint": "^7.20.0", "eslint-config-next": "^11.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^5.0.9", "i18n-extract": "^0.6.7", "jest": "^27.1.0", "lint-staged": "^10.5.4", "nightwind": "^1.1.6", "npm-run-all": "^4.1.5", "postcss": "^8.2.6", "prettier": "^2.2.1", "supertest": "^6.1.6", "tailwindcss": "^2.0.3"}, "resolutions": {"lodash": "^4.17.21", "refractor": "^4.8.1", "@types/react": "17.0.50"}, "license": "MIT"}