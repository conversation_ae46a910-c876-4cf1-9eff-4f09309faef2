"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 🔧 调试：检查onChange回调是否正确传递\n    console.log(\"\\uD83D\\uDD27 LexicalEditor 初始化:\", {\n        hasOnChange: !!onChange,\n        readOnly: readOnly,\n        valueLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n        mounted: mounted,\n        timestamp: new Date().toISOString()\n    });\n    // 🔧 检查全局事件监听器和干扰\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        if (true) {\n            console.log(\"\\uD83D\\uDD27 检查全局事件监听器...\");\n            // 🔧 检查是否有全局的事件监听器\n            var originalAddEventListener = document.addEventListener;\n            var globalListeners = [];\n            // 临时拦截addEventListener来记录全局监听器\n            document.addEventListener = function(type, listener, options) {\n                if ([\n                    \"beforeinput\",\n                    \"input\",\n                    \"keydown\",\n                    \"keypress\"\n                ].includes(type)) {\n                    console.log(\"\\uD83D\\uDD27 检测到全局事件监听器:\", {\n                        type: type,\n                        listener: listener.toString().substring(0, 100),\n                        options: options\n                    });\n                    globalListeners.push({\n                        type: type,\n                        listener: listener,\n                        options: options\n                    });\n                }\n                return originalAddEventListener.call(this, type, listener, options);\n            };\n            // 恢复原始方法\n            setTimeout(function() {\n                document.addEventListener = originalAddEventListener;\n                console.log(\"\\uD83D\\uDD27 全局监听器检查完成，发现:\", globalListeners.length, \"个相关监听器\");\n            }, 1000);\n            // 检查是否有全局的beforeinput监听器\n            var testElement = document.createElement(\"div\");\n            testElement.contentEditable = \"true\";\n            testElement.style.position = \"absolute\";\n            testElement.style.top = \"-1000px\";\n            document.body.appendChild(testElement);\n            var testEvent = new InputEvent(\"beforeinput\", {\n                inputType: \"insertText\",\n                data: \"test\",\n                bubbles: true,\n                cancelable: true\n            });\n            testElement.addEventListener(\"beforeinput\", function(e) {\n                console.log(\"\\uD83D\\uDD27 测试元素接收到beforeinput事件:\", e);\n            });\n            console.log(\"\\uD83D\\uDD27 派发测试beforeinput事件...\");\n            var result = testElement.dispatchEvent(testEvent);\n            console.log(\"\\uD83D\\uDD27 测试事件结果:\", {\n                result: result,\n                defaultPrevented: testEvent.defaultPrevented\n            });\n            setTimeout(function() {\n                document.body.removeChild(testElement);\n            }, 100);\n        }\n    }, []);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 🔧 修复：创建有效的初始编辑器状态\n        editorState: function() {\n            var editor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                namespace: \"LexicalEditor\",\n                nodes: [\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                    _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                    _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                ],\n                onError: function(error) {\n                    return console.error(\"Lexical Error:\", error);\n                }\n            });\n            // 创建基本的编辑器状态\n            return editor.parseEditorState({\n                root: {\n                    children: [\n                        {\n                            children: [],\n                            direction: null,\n                            format: \"\",\n                            indent: 0,\n                            type: \"paragraph\",\n                            version: 1\n                        }, \n                    ],\n                    direction: null,\n                    format: \"\",\n                    indent: 0,\n                    type: \"root\",\n                    version: 1\n                }\n            });\n        }\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        console.log(\"\\uD83D\\uDEA8 handleChange被触发!\", {\n            hasOnChange: !!onChange,\n            tags: Array.from(tags),\n            editorStateExists: !!editorState,\n            timestamp: new Date().toISOString()\n        });\n        if (!onChange) {\n            console.log(\"❌ onChange函数不存在，无法处理编辑器变化\");\n            return;\n        }\n        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n        if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n            console.log(\"⏭️ 跳过历史合并或内容同步触发的更新\");\n            return;\n        }\n        console.log(\"✅ 开始处理编辑器状态变化\");\n        editorState.read(function() {\n            try {\n                // 🔧 添加调试：检查编辑器状态中的列表结构\n                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                var children = root.getChildren();\n                console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                children.forEach(function(child, index) {\n                    console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素:\"), {\n                        type: child.getType(),\n                        textContent: child.getTextContent()\n                    });\n                    if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                        console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                            type: child.getListType(),\n                            childrenCount: child.getChildren().length\n                        });\n                        var listItems = child.getChildren();\n                        listItems.forEach(function(item, itemIndex) {\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                var itemChildren = item.getChildren();\n                                console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                    childrenCount: itemChildren.length,\n                                    textContent: item.getTextContent(),\n                                    hasNestedList: itemChildren.some(function(c) {\n                                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                    })\n                                });\n                            }\n                        });\n                    }\n                });\n                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                var editorStateJSON = JSON.stringify(editorState.toJSON());\n                console.log(\"\\uD83D\\uDD27 准备调用onChange，JSON长度:\", editorStateJSON.length);\n                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑\n                onChange(function() {\n                    console.log(\"\\uD83D\\uDD27 onChange回调被执行，返回JSON内容\");\n                    return editorStateJSON;\n                });\n                console.log(\"✅ onChange调用完成\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D Error in handleChange:\", error);\n            // 如果转换出错，保持原有内容不变\n            }\n        });\n    }, [\n        onChange,\n        value\n    ]);\n    // 调试插件 - 检查编辑器状态\n    var DebugPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器初始化完成\", {\n                isEditable: editor.isEditable(),\n                hasRootElement: !!editor.getRootElement(),\n                readOnly: readOnly,\n                timestamp: new Date().toISOString()\n            });\n            // 🔧 确保编辑器在非只读模式下是可编辑的\n            if (!readOnly && !editor.isEditable()) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 强制设置编辑器为可编辑状态\");\n                editor.setEditable(true);\n            }\n            // 监听所有编辑器更新\n            var removeUpdateListener = editor.registerUpdateListener(function(param) {\n                var editorState = param.editorState, prevEditorState = param.prevEditorState, tags = param.tags;\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器更新\", {\n                    tags: Array.from(tags),\n                    hasChanges: editorState !== prevEditorState,\n                    isEditable: editor.isEditable(),\n                    timestamp: new Date().toISOString()\n                });\n            });\n            // 监听编辑器状态变化\n            var removeEditableListener = editor.registerEditableListener(function(editable) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器可编辑状态变化\", {\n                    editable: editable,\n                    readOnly: readOnly\n                });\n            });\n            return function() {\n                removeUpdateListener();\n                removeEditableListener();\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(DebugPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                console.log(\"\\uD83D\\uDD27 ListExitPlugin: Enter键被按下\");\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 优化版本，避免与用户输入冲突\n    var ContentSyncPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        var _$ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), lastSyncedValue = _$ref1[0], setLastSyncedValue = _$ref1[1];\n        var _$ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUserTyping = _$ref2[0], setIsUserTyping = _$ref2[1];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 🔧 优化：只有当value真正改变且用户没有在输入时才同步\n                if (value !== lastSyncedValue && !isUserTyping) {\n                    console.log(\"\\uD83D\\uDD27 ContentSyncPlugin: 准备同步内容\", {\n                        valueLength: value.length,\n                        lastSyncedLength: lastSyncedValue.length,\n                        isUserTyping: isUserTyping\n                    });\n                    // 使用setTimeout来避免在渲染过程中调用flushSync\n                    setTimeout(function() {\n                        if (value.trim()) {\n                            try {\n                                // 🔧 解析JSON格式的编辑器状态\n                                var editorStateData = JSON.parse(value);\n                                console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                                // 直接设置编辑器状态\n                                var newEditorState = editor.parseEditorState(editorStateData);\n                                editor.setEditorState(newEditorState);\n                                setLastSyncedValue(value);\n                            } catch (jsonError) {\n                                console.error(\"\\uD83D\\uDD27 JSON解析失败，尝试作为Markdown处理:\", jsonError);\n                                // 🔧 修复：如果不是JSON格式，尝试作为Markdown或纯文本处理\n                                if (value.trim() === \"\\n\" || value.trim() === \"\") {\n                                    // 空内容，创建空段落\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                } else {\n                                    // 有内容但不是JSON，可能是旧的Markdown格式\n                                    console.log(\"\\uD83D\\uDD27 检测到非JSON内容，可能是旧格式，创建空编辑器\");\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                }\n                            }\n                        } else {\n                            // 空内容时清空并创建一个空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                            setLastSyncedValue(value);\n                        }\n                    }, 0);\n                }\n            }\n        }, [\n            editor,\n            value,\n            mounted,\n            lastSyncedValue,\n            isUserTyping\n        ]);\n        // 🔧 监听用户输入状态\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (!editor) return;\n            var rootElement = editor.getRootElement();\n            if (!rootElement) return;\n            var typingTimer;\n            var handleInput = function() {\n                setIsUserTyping(true);\n                clearTimeout(typingTimer);\n                typingTimer = setTimeout(function() {\n                    setIsUserTyping(false);\n                }, 1000); // 1秒后认为用户停止输入\n            };\n            rootElement.addEventListener(\"input\", handleInput);\n            rootElement.addEventListener(\"keydown\", handleInput);\n            return function() {\n                rootElement.removeEventListener(\"input\", handleInput);\n                rootElement.removeEventListener(\"keydown\", handleInput);\n                clearTimeout(typingTimer);\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(ContentSyncPlugin, \"SYK262ymxGBaV3Gm+LQPP5D2LGU=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 🔧 新增：DOM状态检查插件\n    var DOMStatePlugin = function() {\n        _s4();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            var checkDOMState = function() {\n                var rootElement = editor.getRootElement();\n                if (rootElement) {\n                    console.log(\"\\uD83D\\uDD27 DOM状态检查:\", {\n                        contentEditable: rootElement.contentEditable,\n                        isContentEditable: rootElement.isContentEditable,\n                        tabIndex: rootElement.tabIndex,\n                        style: {\n                            pointerEvents: getComputedStyle(rootElement).pointerEvents,\n                            userSelect: getComputedStyle(rootElement).userSelect,\n                            cursor: getComputedStyle(rootElement).cursor,\n                            display: getComputedStyle(rootElement).display,\n                            visibility: getComputedStyle(rootElement).visibility\n                        },\n                        hasChildren: rootElement.children.length,\n                        textContent: rootElement.textContent,\n                        innerHTML: rootElement.innerHTML.substring(0, 200)\n                    });\n                    // 🔧 强制设置contentEditable\n                    if (rootElement.contentEditable !== \"true\" && !readOnly) {\n                        console.log(\"\\uD83D\\uDD27 强制设置contentEditable为true\");\n                        rootElement.contentEditable = \"true\";\n                    }\n                    // 🔧 强制设置tabIndex\n                    if (rootElement.tabIndex < 0) {\n                        console.log(\"\\uD83D\\uDD27 设置tabIndex为0\");\n                        rootElement.tabIndex = 0;\n                    }\n                    // 🔧 检查是否有全局事件监听器干扰\n                    var testInput = function() {\n                        console.log(\"\\uD83D\\uDD27 测试输入功能...\");\n                        rootElement.focus();\n                        // 模拟键盘输入\n                        var event = new KeyboardEvent(\"keydown\", {\n                            key: \"a\",\n                            code: \"KeyA\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        var inputEvent = new InputEvent(\"beforeinput\", {\n                            inputType: \"insertText\",\n                            data: \"a\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        console.log(\"\\uD83D\\uDD27 派发测试事件...\");\n                        var keyResult = rootElement.dispatchEvent(event);\n                        var inputResult = rootElement.dispatchEvent(inputEvent);\n                        console.log(\"\\uD83D\\uDD27 事件派发结果:\", {\n                            keyEvent: keyResult,\n                            inputEvent: inputResult,\n                            keyDefaultPrevented: event.defaultPrevented,\n                            inputDefaultPrevented: inputEvent.defaultPrevented\n                        });\n                    };\n                    // 延迟测试，确保编辑器完全初始化\n                    setTimeout(testInput, 1000);\n                }\n            };\n            // 立即检查\n            checkDOMState();\n            // 定期检查\n            var interval = setInterval(checkDOMState, 5000);\n            return function() {\n                return clearInterval(interval);\n            };\n        }, [\n            editor,\n            readOnly\n        ]);\n        return null;\n    };\n    _s4(DOMStatePlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false,\n                                style: {\n                                    userSelect: \"text\",\n                                    WebkitUserSelect: \"text\",\n                                    MozUserSelect: \"text\",\n                                    msUserSelect: \"text\",\n                                    cursor: \"text\",\n                                    minHeight: \"200px\",\n                                    // 🔧 强制设置可编辑相关样式\n                                    pointerEvents: \"auto\",\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                onKeyDown: function(e) {\n                                    console.log(\"\\uD83C\\uDFB9 键盘按下:\", {\n                                        key: e.key,\n                                        code: e.code,\n                                        ctrlKey: e.ctrlKey,\n                                        shiftKey: e.shiftKey,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                    // 🔧 检查事件是否被阻止\n                                    if (e.defaultPrevented) {\n                                        console.warn(\"⚠️ 键盘事件被阻止了!\");\n                                    }\n                                },\n                                onInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onBeforeInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD beforeinput事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                    // 🔧 检查beforeinput事件是否被阻止\n                                    if (e.defaultPrevented) {\n                                        console.warn(\"⚠️ beforeinput事件被阻止了!\");\n                                    }\n                                },\n                                onFocus: function(e) {\n                                    console.log(\"\\uD83C\\uDFAF 编辑器获得焦点:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                },\n                                onBlur: function() {\n                                    console.log(\"\\uD83D\\uDE34 编辑器失去焦点\");\n                                },\n                                onClick: function(e) {\n                                    console.log(\"\\uD83D\\uDDB1️ 编辑器点击:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 808,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 809,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 810,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 814,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DOMStatePlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 824,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 727,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 726,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 725,\n        columnNumber: 9\n    }, _this);\n}, \"iuZ+b0O1CXZIV7Hez3f+DTYXPqg=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"iuZ+b0O1CXZIV7Hez3f+DTYXPqg=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});