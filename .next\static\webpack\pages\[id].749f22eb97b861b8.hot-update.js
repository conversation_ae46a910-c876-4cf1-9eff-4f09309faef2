"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 🔧 调试：检查onChange回调是否正确传递\n    console.log(\"\\uD83D\\uDD27 LexicalEditor 初始化:\", {\n        hasOnChange: !!onChange,\n        readOnly: readOnly,\n        valueLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n        mounted: mounted,\n        timestamp: new Date().toISOString()\n    });\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 🔧 修复：创建有效的初始编辑器状态\n        editorState: function() {\n            var editor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                namespace: \"LexicalEditor\",\n                nodes: [\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                    _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                    _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                ],\n                onError: function(error) {\n                    return console.error(\"Lexical Error:\", error);\n                }\n            });\n            // 创建基本的编辑器状态\n            return editor.parseEditorState({\n                root: {\n                    children: [\n                        {\n                            children: [],\n                            direction: null,\n                            format: \"\",\n                            indent: 0,\n                            type: \"paragraph\",\n                            version: 1\n                        }, \n                    ],\n                    direction: null,\n                    format: \"\",\n                    indent: 0,\n                    type: \"root\",\n                    version: 1\n                }\n            });\n        }\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        console.log(\"\\uD83D\\uDEA8 handleChange被触发!\", {\n            hasOnChange: !!onChange,\n            tags: Array.from(tags),\n            editorStateExists: !!editorState,\n            timestamp: new Date().toISOString()\n        });\n        if (!onChange) {\n            console.log(\"❌ onChange函数不存在，无法处理编辑器变化\");\n            return;\n        }\n        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n        if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n            console.log(\"⏭️ 跳过历史合并或内容同步触发的更新\");\n            return;\n        }\n        console.log(\"✅ 开始处理编辑器状态变化\");\n        editorState.read(function() {\n            try {\n                // 🔧 添加调试：检查编辑器状态中的列表结构\n                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                var children = root.getChildren();\n                console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                children.forEach(function(child, index) {\n                    console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素:\"), {\n                        type: child.getType(),\n                        textContent: child.getTextContent()\n                    });\n                    if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                        console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                            type: child.getListType(),\n                            childrenCount: child.getChildren().length\n                        });\n                        var listItems = child.getChildren();\n                        listItems.forEach(function(item, itemIndex) {\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                var itemChildren = item.getChildren();\n                                console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                    childrenCount: itemChildren.length,\n                                    textContent: item.getTextContent(),\n                                    hasNestedList: itemChildren.some(function(c) {\n                                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                    })\n                                });\n                            }\n                        });\n                    }\n                });\n                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                var editorStateJSON = JSON.stringify(editorState.toJSON());\n                console.log(\"\\uD83D\\uDD27 准备调用onChange，JSON长度:\", editorStateJSON.length);\n                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑\n                onChange(function() {\n                    console.log(\"\\uD83D\\uDD27 onChange回调被执行，返回JSON内容\");\n                    return editorStateJSON;\n                });\n                console.log(\"✅ onChange调用完成\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D Error in handleChange:\", error);\n            // 如果转换出错，保持原有内容不变\n            }\n        });\n    }, [\n        onChange,\n        value\n    ]);\n    // 调试插件 - 检查编辑器状态\n    var DebugPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器初始化完成\", {\n                isEditable: editor.isEditable(),\n                hasRootElement: !!editor.getRootElement(),\n                readOnly: readOnly,\n                timestamp: new Date().toISOString()\n            });\n            // 🔧 确保编辑器在非只读模式下是可编辑的\n            if (!readOnly && !editor.isEditable()) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 强制设置编辑器为可编辑状态\");\n                editor.setEditable(true);\n            }\n            // 监听所有编辑器更新\n            var removeUpdateListener = editor.registerUpdateListener(function(param) {\n                var editorState = param.editorState, prevEditorState = param.prevEditorState, tags = param.tags;\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器更新\", {\n                    tags: Array.from(tags),\n                    hasChanges: editorState !== prevEditorState,\n                    isEditable: editor.isEditable(),\n                    timestamp: new Date().toISOString()\n                });\n            });\n            // 监听编辑器状态变化\n            var removeEditableListener = editor.registerEditableListener(function(editable) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器可编辑状态变化\", {\n                    editable: editable,\n                    readOnly: readOnly\n                });\n            });\n            return function() {\n                removeUpdateListener();\n                removeEditableListener();\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(DebugPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                console.log(\"\\uD83D\\uDD27 ListExitPlugin: Enter键被按下\");\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 优化版本，避免与用户输入冲突\n    var ContentSyncPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        var _$ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), lastSyncedValue = _$ref1[0], setLastSyncedValue = _$ref1[1];\n        var _$ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUserTyping = _$ref2[0], setIsUserTyping = _$ref2[1];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 🔧 优化：只有当value真正改变且用户没有在输入时才同步\n                if (value !== lastSyncedValue && !isUserTyping) {\n                    console.log(\"\\uD83D\\uDD27 ContentSyncPlugin: 准备同步内容\", {\n                        valueLength: value.length,\n                        lastSyncedLength: lastSyncedValue.length,\n                        isUserTyping: isUserTyping\n                    });\n                    // 使用setTimeout来避免在渲染过程中调用flushSync\n                    setTimeout(function() {\n                        if (value.trim()) {\n                            try {\n                                // 🔧 解析JSON格式的编辑器状态\n                                var editorStateData = JSON.parse(value);\n                                console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                                // 直接设置编辑器状态\n                                var newEditorState = editor.parseEditorState(editorStateData);\n                                editor.setEditorState(newEditorState);\n                                setLastSyncedValue(value);\n                            } catch (jsonError) {\n                                console.error(\"\\uD83D\\uDD27 JSON解析失败，尝试作为Markdown处理:\", jsonError);\n                                // 🔧 修复：如果不是JSON格式，尝试作为Markdown或纯文本处理\n                                if (value.trim() === \"\\n\" || value.trim() === \"\") {\n                                    // 空内容，创建空段落\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                } else {\n                                    // 有内容但不是JSON，可能是旧的Markdown格式\n                                    console.log(\"\\uD83D\\uDD27 检测到非JSON内容，可能是旧格式，创建空编辑器\");\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                }\n                            }\n                        } else {\n                            // 空内容时清空并创建一个空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                            setLastSyncedValue(value);\n                        }\n                    }, 0);\n                }\n            }\n        }, [\n            editor,\n            value,\n            mounted,\n            lastSyncedValue,\n            isUserTyping\n        ]);\n        // 🔧 监听用户输入状态\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (!editor) return;\n            var rootElement = editor.getRootElement();\n            if (!rootElement) return;\n            var typingTimer;\n            var handleInput = function() {\n                setIsUserTyping(true);\n                clearTimeout(typingTimer);\n                typingTimer = setTimeout(function() {\n                    setIsUserTyping(false);\n                }, 1000); // 1秒后认为用户停止输入\n            };\n            rootElement.addEventListener(\"input\", handleInput);\n            rootElement.addEventListener(\"keydown\", handleInput);\n            return function() {\n                rootElement.removeEventListener(\"input\", handleInput);\n                rootElement.removeEventListener(\"keydown\", handleInput);\n                clearTimeout(typingTimer);\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(ContentSyncPlugin, \"SYK262ymxGBaV3Gm+LQPP5D2LGU=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 🔧 新增：DOM状态检查插件\n    var DOMStatePlugin = function() {\n        _s4();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            var checkDOMState = function() {\n                var rootElement = editor.getRootElement();\n                if (rootElement) {\n                    console.log(\"\\uD83D\\uDD27 DOM状态检查:\", {\n                        contentEditable: rootElement.contentEditable,\n                        isContentEditable: rootElement.isContentEditable,\n                        tabIndex: rootElement.tabIndex,\n                        style: {\n                            pointerEvents: getComputedStyle(rootElement).pointerEvents,\n                            userSelect: getComputedStyle(rootElement).userSelect,\n                            cursor: getComputedStyle(rootElement).cursor,\n                            display: getComputedStyle(rootElement).display,\n                            visibility: getComputedStyle(rootElement).visibility\n                        },\n                        hasChildren: rootElement.children.length,\n                        textContent: rootElement.textContent,\n                        innerHTML: rootElement.innerHTML.substring(0, 200)\n                    });\n                    // 🔧 强制设置contentEditable\n                    if (rootElement.contentEditable !== \"true\" && !readOnly) {\n                        console.log(\"\\uD83D\\uDD27 强制设置contentEditable为true\");\n                        rootElement.contentEditable = \"true\";\n                    }\n                    // 🔧 强制设置tabIndex\n                    if (rootElement.tabIndex < 0) {\n                        console.log(\"\\uD83D\\uDD27 设置tabIndex为0\");\n                        rootElement.tabIndex = 0;\n                    }\n                    // 🔧 检查是否有全局事件监听器干扰\n                    var testInput = function() {\n                        console.log(\"\\uD83D\\uDD27 测试输入功能...\");\n                        rootElement.focus();\n                        // 模拟键盘输入\n                        var event = new KeyboardEvent(\"keydown\", {\n                            key: \"a\",\n                            code: \"KeyA\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        var inputEvent = new InputEvent(\"beforeinput\", {\n                            inputType: \"insertText\",\n                            data: \"a\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        console.log(\"\\uD83D\\uDD27 派发测试事件...\");\n                        var keyResult = rootElement.dispatchEvent(event);\n                        var inputResult = rootElement.dispatchEvent(inputEvent);\n                        console.log(\"\\uD83D\\uDD27 事件派发结果:\", {\n                            keyEvent: keyResult,\n                            inputEvent: inputResult,\n                            keyDefaultPrevented: event.defaultPrevented,\n                            inputDefaultPrevented: inputEvent.defaultPrevented\n                        });\n                    };\n                    // 延迟测试，确保编辑器完全初始化\n                    setTimeout(testInput, 1000);\n                }\n            };\n            // 立即检查\n            checkDOMState();\n            // 定期检查\n            var interval = setInterval(checkDOMState, 5000);\n            return function() {\n                return clearInterval(interval);\n            };\n        }, [\n            editor,\n            readOnly\n        ]);\n        return null;\n    };\n    _s4(DOMStatePlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false,\n                                style: {\n                                    userSelect: \"text\",\n                                    WebkitUserSelect: \"text\",\n                                    MozUserSelect: \"text\",\n                                    msUserSelect: \"text\",\n                                    cursor: \"text\",\n                                    minHeight: \"200px\"\n                                },\n                                onKeyDown: function(e) {\n                                    console.log(\"\\uD83C\\uDFB9 键盘按下:\", {\n                                        key: e.key,\n                                        code: e.code,\n                                        ctrlKey: e.ctrlKey,\n                                        shiftKey: e.shiftKey,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onFocus: function(e) {\n                                    console.log(\"\\uD83C\\uDFAF 编辑器获得焦点:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                },\n                                onBlur: function() {\n                                    console.log(\"\\uD83D\\uDE34 编辑器失去焦点\");\n                                },\n                                onClick: function(e) {\n                                    console.log(\"\\uD83D\\uDDB1️ 编辑器点击:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 731,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DOMStatePlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 667,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 666,\n        columnNumber: 9\n    }, _this);\n}, \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});