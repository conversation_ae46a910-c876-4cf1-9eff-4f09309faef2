/**
 * Lexical Editor Component
 * Migrated from TipTap to Lexical for better performance and modern architecture
 */

import { useImperativeHandle, forwardRef, useCallback, useEffect, useState } from 'react';
import { $getRoot, $createParagraphNode, $createTextNode, EditorState, $getSelection, $isRangeSelection, KEY_ENTER_COMMAND, COMMAND_PRIORITY_HIGH, INSERT_PARAGRAPH_COMMAND, ParagraphNode, $isParagraphNode, createEditor } from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import { TRANSFORMERS, $convertToMarkdownString, $convertFromMarkdownString, ElementTransformer, TextFormatTransformer, CHECK_LIST } from '@lexical/markdown';

import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode, $isListItemNode, $isListNode, $createListNode, $createListItemNode } from '@lexical/list';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { use100vh } from 'react-div-100vh';
import useMounted from 'libs/web/hooks/use-mounted';
import useI18n from 'libs/web/hooks/use-i18n';

// Import custom plugins and nodes
import SlashCommandsPlugin from './plugins/slash-commands-plugin';
import FloatingToolbarPlugin from './plugins/floating-toolbar-plugin';
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import HighlightPlugin from './plugins/highlight-plugin';
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin';
import ImagePlugin from './plugins/image-plugin';
import IMEPlugin from './plugins/ime-plugin';
import { ImageNode, $createImageNode, $isImageNode } from './nodes/image-node';
import { HorizontalRuleNode, $isHorizontalRuleNode, $createHorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin';

// 使用Lexical原生的ListItemNode，不需要自定义TaskList节点
// 使用Lexical内置的highlight格式，不需要自定义HighlightNode

export interface LexicalEditorProps {
    readOnly?: boolean;
    isPreview?: boolean;
    value?: string;
    onChange?: (value: () => string) => void;
    onCreateLink?: (title: string) => Promise<string>;
    onSearchLink?: (term: string) => Promise<any[]>;
    onClickLink?: (href: string, event: any) => void;
    onHoverLink?: (event: any) => boolean;
    className?: string;
}

export interface LexicalEditorRef {
    focusAtEnd: () => void;
    focusAtStart: () => void;
}

const theme = {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    quote: 'editor-quote',
    heading: {
        h1: 'editor-heading-h1',
        h2: 'editor-heading-h2',
        h3: 'editor-heading-h3',
        h4: 'editor-heading-h4',
        h5: 'editor-heading-h5',
        h6: 'editor-heading-h6',
    },
    list: {
        nested: {
            listitem: 'editor-nested-listitem',
        },
        ol: 'editor-list-ol',
        ul: 'editor-list-ul',
        listitem: 'editor-listitem',
        // 按深度定义不同的列表样式 - 这是关键！
        olDepth: [
            'editor-list-ol-1',  // 1, 2, 3...
            'editor-list-ol-2',  // a, b, c...
            'editor-list-ol-3',  // i, ii, iii...
            'editor-list-ol-4',  // A, B, C...
            'editor-list-ol-5'   // I, II, III...
        ],
        ulDepth: [
            'editor-list-ul-1',  // •
            'editor-list-ul-2',  // ○
            'editor-list-ul-3',  // ▪
            'editor-list-ul-4',  // ▫
            'editor-list-ul-5'   // ‣
        ],
        checklist: 'PlaygroundEditorTheme__checklist',
        listitemChecked: 'PlaygroundEditorTheme__listItemChecked',
        listitemUnchecked: 'PlaygroundEditorTheme__listItemUnchecked',
    },
    // Lexical 0.32.1 原生缩进支持
    indent: 'lexical-indent',
    image: 'editor-image',
    link: 'editor-link',
    text: {
        bold: 'editor-text-bold',
        italic: 'editor-text-italic',
        overflowed: 'editor-text-overflowed',
        hashtag: 'editor-text-hashtag',
        underline: 'editor-text-underline',
        strikethrough: 'editor-text-strikethrough',
        underlineStrikethrough: 'editor-text-underlineStrikethrough',
        code: 'editor-text-code',
        highlight: 'editor-text-highlight',
    },
    code: 'editor-code',
    codeHighlight: {
        atrule: 'editor-tokenAttr',
        attr: 'editor-tokenAttr',
        boolean: 'editor-tokenProperty',
        builtin: 'editor-tokenSelector',
        cdata: 'editor-tokenComment',
        char: 'editor-tokenSelector',
        class: 'editor-tokenFunction',
        'class-name': 'editor-tokenFunction',
        comment: 'editor-tokenComment',
        constant: 'editor-tokenProperty',
        deleted: 'editor-tokenProperty',
        doctype: 'editor-tokenComment',
        entity: 'editor-tokenOperator',
        function: 'editor-tokenFunction',
        important: 'editor-tokenVariable',
        inserted: 'editor-tokenSelector',
        keyword: 'editor-tokenAttr',
        namespace: 'editor-tokenVariable',
        number: 'editor-tokenProperty',
        operator: 'editor-tokenOperator',
        prolog: 'editor-tokenComment',
        property: 'editor-tokenProperty',
        punctuation: 'editor-tokenPunctuation',
        regex: 'editor-tokenVariable',
        selector: 'editor-tokenSelector',
        string: 'editor-tokenSelector',
        symbol: 'editor-tokenProperty',
        tag: 'editor-tokenProperty',
        url: 'editor-tokenOperator',
        variable: 'editor-tokenVariable',
    },
};

function Placeholder() {
    const { t } = useI18n();
    return <div className="editor-placeholder">{t('Start writing...')}</div>;
}

const LexicalEditor = forwardRef<LexicalEditorRef, LexicalEditorProps>(({
    readOnly = false,
    value = '',
    onChange,
    onClickLink,
    onHoverLink,
    className = '',
}, ref) => {
    const height = use100vh();
    const mounted = useMounted();

    // 🔧 调试：检查onChange回调是否正确传递
    console.log('🔧 LexicalEditor 初始化:', {
        hasOnChange: !!onChange,
        readOnly,
        valueLength: value?.length || 0,
        mounted,
        timestamp: new Date().toISOString()
    });

    // 🔧 检查全局事件监听器
    useEffect(() => {
        if (typeof window !== 'undefined') {
            console.log('🔧 检查全局事件监听器...');

            // 检查是否有全局的beforeinput监听器
            const testElement = document.createElement('div');
            testElement.contentEditable = 'true';
            document.body.appendChild(testElement);

            const testEvent = new InputEvent('beforeinput', {
                inputType: 'insertText',
                data: 'test',
                bubbles: true,
                cancelable: true
            });

            testElement.addEventListener('beforeinput', (e) => {
                console.log('🔧 测试元素接收到beforeinput事件:', e);
            });

            console.log('🔧 派发测试beforeinput事件...');
            const result = testElement.dispatchEvent(testEvent);
            console.log('🔧 测试事件结果:', {
                result,
                defaultPrevented: testEvent.defaultPrevented
            });

            document.body.removeChild(testElement);
        }
    }, []);

    const initialConfig = {
        namespace: 'LexicalEditor',
        theme,
        onError(error: Error) {
            console.error('Lexical Error:', error);
        },
        nodes: [
            HeadingNode,
            ListNode,
            ListItemNode,
            QuoteNode,
            CodeNode,
            CodeHighlightNode,
            AutoLinkNode,
            LinkNode,
            // Lexical原生的ListItemNode已经支持checkbox功能
            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode
            ImageNode,
            HorizontalRuleNode,
        ],
        editable: !readOnly,
        // 🔧 修复：创建有效的初始编辑器状态
        editorState: () => {
            const editor = createEditor({
                namespace: 'LexicalEditor',
                nodes: [
                    HeadingNode,
                    ListNode,
                    ListItemNode,
                    QuoteNode,
                    CodeNode,
                    CodeHighlightNode,
                    AutoLinkNode,
                    LinkNode,
                    ImageNode,
                    HorizontalRuleNode,
                ],
                onError: (error: Error) => console.error('Lexical Error:', error),
            });

            // 创建基本的编辑器状态
            return editor.parseEditorState({
                root: {
                    children: [
                        {
                            children: [],
                            direction: null,
                            format: '',
                            indent: 0,
                            type: 'paragraph',
                            version: 1,
                        },
                    ],
                    direction: null,
                    format: '',
                    indent: 0,
                    type: 'root',
                    version: 1,
                },
            });
        },
    };

    // 创建自定义transformers，包含图片支持
    const IMAGE_TRANSFORMER: ElementTransformer = {
        dependencies: [ImageNode],
        export: (node) => {
            if (!$isImageNode(node)) {
                return null;
            }
            return `![${node.getAltText()}](${node.getSrc()})`;
        },
        regExp: /!\[([^\]]*)\]\(([^)]+)\)/,
        replace: (parentNode, children, match) => {
            const [, altText, src] = match;
            const imageNode = $createImageNode({
                altText,
                src,
                maxWidth: 800, // 设置最大宽度
            });
            children.forEach(child => child.remove());
            parentNode.append(imageNode);
        },
        type: 'element',
    };

    // 创建自定义的下划线转换器，使用 <u>text</u> 语法
    const UNDERLINE_TRANSFORMER: TextFormatTransformer = {
        format: ['underline'],
        tag: '<u>',
        type: 'text-format',
    };

    // 移除段落缩进转换器 - 专注于列表缩进功能
    // 段落缩进不是标准markdown语法，我们只处理列表缩进

    // 创建水平分割线转换器
    const HR_TRANSFORMER: ElementTransformer = {
        dependencies: [HorizontalRuleNode],
        export: (node) => {
            return $isHorizontalRuleNode(node) ? '---' : null;
        },
        regExp: /^(---|\*\*\*|___)\s?$/,
        replace: (parentNode, children, match, isImport) => {
            const line = $createHorizontalRuleNode();
            if (isImport || parentNode.getNextSibling() != null) {
                parentNode.replace(line);
            } else {
                parentNode.insertBefore(line);
            }
            line.selectNext();
        },
        type: 'element',
    };

    // 🔧 JSON格式保存 - 完全使用Lexical原生格式

    // 使用标准的Lexical transformers
    const customTransformers = [
        ...TRANSFORMERS,
        // 添加我们的自定义转换器
        HR_TRANSFORMER,
        UNDERLINE_TRANSFORMER,
        IMAGE_TRANSFORMER
    ];

    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题

    const handleChange = useCallback((editorState: EditorState, _editor: any, tags: Set<string>) => {
        console.log('🚨 handleChange被触发!', {
            hasOnChange: !!onChange,
            tags: Array.from(tags),
            editorStateExists: !!editorState,
            timestamp: new Date().toISOString()
        });

        if (!onChange) {
            console.log('❌ onChange函数不存在，无法处理编辑器变化');
            return;
        }

        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新
        if (tags.has('history-merge') || tags.has('content-sync')) {
            console.log('⏭️ 跳过历史合并或内容同步触发的更新');
            return;
        }

        console.log('✅ 开始处理编辑器状态变化');

        editorState.read(() => {
            try {
                // 🔧 添加调试：检查编辑器状态中的列表结构
                const root = $getRoot();
                const children = root.getChildren();
                console.log('🔧 编辑器根节点子元素数量:', children.length);

                children.forEach((child, index) => {
                    console.log(`🔧 第${index}个子元素:`, {
                        type: child.getType(),
                        textContent: child.getTextContent()
                    });

                    if ($isListNode(child)) {
                        console.log(`🔧 第${index}个子元素是列表:`, {
                            type: child.getListType(),
                            childrenCount: child.getChildren().length
                        });

                        const listItems = child.getChildren();
                        listItems.forEach((item, itemIndex) => {
                            if ($isListItemNode(item)) {
                                const itemChildren = item.getChildren();
                                console.log(`🔧   列表项${itemIndex}:`, {
                                    childrenCount: itemChildren.length,
                                    textContent: item.getTextContent(),
                                    hasNestedList: itemChildren.some(c => $isListNode(c))
                                });
                            }
                        });
                    }
                });

                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系
                const editorStateJSON = JSON.stringify(editorState.toJSON());

                console.log('🔧 准备调用onChange，JSON长度:', editorStateJSON.length);

                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑
                onChange(() => {
                    console.log('🔧 onChange回调被执行，返回JSON内容');
                    return editorStateJSON;
                });

                console.log('✅ onChange调用完成');
            } catch (error) {
                console.error('🔍 Error in handleChange:', error);
                // 如果转换出错，保持原有内容不变
            }
        });
    }, [onChange, value]);

    // 调试插件 - 检查编辑器状态
    const DebugPlugin = () => {
        const [editor] = useLexicalComposerContext();

        useEffect(() => {
            console.log('🔧 DebugPlugin: 编辑器初始化完成', {
                isEditable: editor.isEditable(),
                hasRootElement: !!editor.getRootElement(),
                readOnly: readOnly,
                timestamp: new Date().toISOString()
            });

            // 🔧 确保编辑器在非只读模式下是可编辑的
            if (!readOnly && !editor.isEditable()) {
                console.log('🔧 DebugPlugin: 强制设置编辑器为可编辑状态');
                editor.setEditable(true);
            }

            // 监听所有编辑器更新
            const removeUpdateListener = editor.registerUpdateListener(({ editorState, prevEditorState, tags }) => {
                console.log('🔧 DebugPlugin: 编辑器更新', {
                    tags: Array.from(tags),
                    hasChanges: editorState !== prevEditorState,
                    isEditable: editor.isEditable(),
                    timestamp: new Date().toISOString()
                });
            });

            // 监听编辑器状态变化
            const removeEditableListener = editor.registerEditableListener((editable) => {
                console.log('🔧 DebugPlugin: 编辑器可编辑状态变化', { editable, readOnly });
            });

            return () => {
                removeUpdateListener();
                removeEditableListener();
            };
        }, [editor]);

        return null;
    };

    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑
    const ListExitPlugin = () => {
        const [editor] = useLexicalComposerContext();

        useEffect(() => {
            return editor.registerCommand(
                KEY_ENTER_COMMAND,
                (event: KeyboardEvent | null) => {
                    console.log('🔧 ListExitPlugin: Enter键被按下');
                    const selection = $getSelection();
                    if (!$isRangeSelection(selection)) {
                        return false;
                    }

                    const anchorNode = selection.anchor.getNode();

                    // 检查是否在空的列表项中
                    if ($isListItemNode(anchorNode)) {
                        const textContent = anchorNode.getTextContent().trim();

                        if (textContent === '') {
                            const listNode = anchorNode.getParent();

                            if ($isListNode(listNode)) {
                                // 如果是空的列表项，退出列表
                                event?.preventDefault();

                                // 创建新段落并在列表后插入
                                const paragraph = $createParagraphNode();
                                listNode.insertAfter(paragraph);

                                // 删除空的列表项
                                anchorNode.remove();

                                // 选中新段落
                                paragraph.select();

                                return true;
                            }
                        }
                    }

                    return false;
                },
                COMMAND_PRIORITY_HIGH
            );
        }, [editor]);

        return null;
    };

    // 内容同步组件 - 优化版本，避免与用户输入冲突
    const ContentSyncPlugin = () => {
        const [editor] = useLexicalComposerContext();
        const [lastSyncedValue, setLastSyncedValue] = useState<string>('');
        const [isUserTyping, setIsUserTyping] = useState(false);

        useEffect(() => {
            if (editor && value !== undefined && mounted) {
                // 🔧 优化：只有当value真正改变且用户没有在输入时才同步
                if (value !== lastSyncedValue && !isUserTyping) {
                    console.log('🔧 ContentSyncPlugin: 准备同步内容', {
                        valueLength: value.length,
                        lastSyncedLength: lastSyncedValue.length,
                        isUserTyping
                    });

                    // 使用setTimeout来避免在渲染过程中调用flushSync
                    setTimeout(() => {
                        if (value.trim()) {
                            try {
                                // 🔧 解析JSON格式的编辑器状态
                                const editorStateData = JSON.parse(value);
                                console.log('🔧 加载Lexical JSON格式内容');

                                // 直接设置编辑器状态
                                const newEditorState = editor.parseEditorState(editorStateData);
                                editor.setEditorState(newEditorState);
                                setLastSyncedValue(value);
                            } catch (jsonError) {
                                console.error('🔧 JSON解析失败，尝试作为Markdown处理:', jsonError);
                                // 🔧 修复：如果不是JSON格式，尝试作为Markdown或纯文本处理
                                if (value.trim() === '\n' || value.trim() === '') {
                                    // 空内容，创建空段落
                                    editor.update(() => {
                                        const root = $getRoot();
                                        root.clear();
                                        const paragraph = $createParagraphNode();
                                        root.append(paragraph);
                                    });
                                    setLastSyncedValue(value);
                                } else {
                                    // 有内容但不是JSON，可能是旧的Markdown格式
                                    console.log('🔧 检测到非JSON内容，可能是旧格式，创建空编辑器');
                                    editor.update(() => {
                                        const root = $getRoot();
                                        root.clear();
                                        const paragraph = $createParagraphNode();
                                        root.append(paragraph);
                                    });
                                    setLastSyncedValue(value);
                                }
                            }
                        } else {
                            // 空内容时清空并创建一个空段落
                            editor.update(() => {
                                const root = $getRoot();
                                root.clear();
                                const paragraph = $createParagraphNode();
                                root.append(paragraph);
                            });
                            setLastSyncedValue(value);
                        }
                    }, 0);
                }
            }
        }, [editor, value, mounted, lastSyncedValue, isUserTyping]);

        // 🔧 监听用户输入状态
        useEffect(() => {
            if (!editor) return;

            const rootElement = editor.getRootElement();
            if (!rootElement) return;

            let typingTimer: NodeJS.Timeout;

            const handleInput = () => {
                setIsUserTyping(true);
                clearTimeout(typingTimer);
                typingTimer = setTimeout(() => {
                    setIsUserTyping(false);
                }, 1000); // 1秒后认为用户停止输入
            };

            rootElement.addEventListener('input', handleInput);
            rootElement.addEventListener('keydown', handleInput);

            return () => {
                rootElement.removeEventListener('input', handleInput);
                rootElement.removeEventListener('keydown', handleInput);
                clearTimeout(typingTimer);
            };
        }, [editor]);

        return null;
    };

    // 🔧 新增：DOM状态检查插件
    const DOMStatePlugin = () => {
        const [editor] = useLexicalComposerContext();

        useEffect(() => {
            const checkDOMState = () => {
                const rootElement = editor.getRootElement();
                if (rootElement) {
                    console.log('🔧 DOM状态检查:', {
                        contentEditable: rootElement.contentEditable,
                        isContentEditable: rootElement.isContentEditable,
                        tabIndex: rootElement.tabIndex,
                        style: {
                            pointerEvents: getComputedStyle(rootElement).pointerEvents,
                            userSelect: getComputedStyle(rootElement).userSelect,
                            cursor: getComputedStyle(rootElement).cursor,
                            display: getComputedStyle(rootElement).display,
                            visibility: getComputedStyle(rootElement).visibility
                        },
                        hasChildren: rootElement.children.length,
                        textContent: rootElement.textContent,
                        innerHTML: rootElement.innerHTML.substring(0, 200)
                    });

                    // 🔧 强制设置contentEditable
                    if (rootElement.contentEditable !== 'true' && !readOnly) {
                        console.log('🔧 强制设置contentEditable为true');
                        rootElement.contentEditable = 'true';
                    }

                    // 🔧 强制设置tabIndex
                    if (rootElement.tabIndex < 0) {
                        console.log('🔧 设置tabIndex为0');
                        rootElement.tabIndex = 0;
                    }

                    // 🔧 检查是否有全局事件监听器干扰
                    const testInput = () => {
                        console.log('🔧 测试输入功能...');
                        rootElement.focus();

                        // 模拟键盘输入
                        const event = new KeyboardEvent('keydown', {
                            key: 'a',
                            code: 'KeyA',
                            bubbles: true,
                            cancelable: true
                        });

                        const inputEvent = new InputEvent('beforeinput', {
                            inputType: 'insertText',
                            data: 'a',
                            bubbles: true,
                            cancelable: true
                        });

                        console.log('🔧 派发测试事件...');
                        const keyResult = rootElement.dispatchEvent(event);
                        const inputResult = rootElement.dispatchEvent(inputEvent);

                        console.log('🔧 事件派发结果:', {
                            keyEvent: keyResult,
                            inputEvent: inputResult,
                            keyDefaultPrevented: event.defaultPrevented,
                            inputDefaultPrevented: inputEvent.defaultPrevented
                        });
                    };

                    // 延迟测试，确保编辑器完全初始化
                    setTimeout(testInput, 1000);
                }
            };

            // 立即检查
            checkDOMState();

            // 定期检查
            const interval = setInterval(checkDOMState, 5000);

            return () => clearInterval(interval);
        }, [editor, readOnly]);

        return null;
    };

    useImperativeHandle(ref, () => ({
        focusAtEnd: () => {
            // TODO: Implement focus at end
        },
        focusAtStart: () => {
            // TODO: Implement focus at start
        },
    }));

    if (!mounted) {
        return null;
    }

    return (
        <div className={`lexical-editor ${className}`}>
            <LexicalComposer initialConfig={initialConfig}>
                <div className="editor-container">
                    <RichTextPlugin
                        contentEditable={
                            <ContentEditable
                                className="editor-input focus:outline-none w-full"
                                spellCheck={false}
                                style={{
                                    userSelect: 'text',
                                    WebkitUserSelect: 'text',
                                    MozUserSelect: 'text',
                                    msUserSelect: 'text',
                                    cursor: 'text',
                                    minHeight: '200px'
                                }}
                                onKeyDown={(e) => {
                                    console.log('🎹 键盘按下:', {
                                        key: e.key,
                                        code: e.code,
                                        ctrlKey: e.ctrlKey,
                                        shiftKey: e.shiftKey,
                                        defaultPrevented: e.defaultPrevented,
                                        target: e.target,
                                        timestamp: new Date().toISOString()
                                    });
                                }}
                                onInput={(e) => {
                                    console.log('📝 输入事件:', {
                                        inputType: (e as any).inputType,
                                        data: (e as any).data,
                                        target: e.target,
                                        timestamp: new Date().toISOString()
                                    });
                                }}
                                onFocus={(e) => {
                                    console.log('🎯 编辑器获得焦点:', {
                                        target: e.target,
                                        contentEditable: (e.target as any).contentEditable,
                                        isContentEditable: (e.target as any).isContentEditable
                                    });
                                }}
                                onBlur={() => {
                                    console.log('😴 编辑器失去焦点');
                                }}
                                onClick={(e) => {
                                    console.log('🖱️ 编辑器点击:', {
                                        target: e.target,
                                        contentEditable: (e.target as any).contentEditable,
                                        isContentEditable: (e.target as any).isContentEditable
                                    });
                                }}
                            />
                        }
                        placeholder={<Placeholder />}
                        ErrorBoundary={LexicalErrorBoundary}
                    />
                    <HistoryPlugin />
                    <AutoFocusPlugin />
                    <LinkPlugin />
                    <ListPlugin />
                    <CheckListPlugin />
                    <MarkdownShortcutPlugin transformers={customTransformers} />
                    <SlashCommandsPlugin />
                    <FloatingToolbarPlugin />
                    <HighlightPlugin />
                    <ImagePlugin />
                    <HorizontalRulePlugin />
                    <IMEPlugin enabled={true} debug={process.env.NODE_ENV === 'development'} />

                    <DebugPlugin />
                    <DOMStatePlugin />
                    <ListExitPlugin />

                    {/* 内容同步和onChange监听器 */}
                    <ContentSyncPlugin />
                    <OnChangePlugin
                        onChange={handleChange}
                        ignoreHistoryMergeTagChange={true}
                        ignoreSelectionChange={true}
                    />
                </div>
            </LexicalComposer>
            <style jsx global>{`
                .lexical-editor {
                    position: relative;
                }

                .editor-container {
                    position: relative;
                }

                .editor-input {
                    outline: none;
                    padding: 1rem 0;
                    min-height: calc(${height ? height + 'px' : '100vh'} - 14rem);
                    padding-bottom: 10rem;
                    width: 100%;
                    max-width: none;
                    line-height: 1.7;
                    font-size: 1rem;
                    color: inherit;
                    -webkit-spellcheck: false;
                    -moz-spellcheck: false;
                    -ms-spellcheck: false;
                    spellcheck: false;
                }

                /* Lexical 0.32.1 原生缩进样式支持 - 基于官方PlaygroundEditorTheme */
                .lexical-editor {
                    --lexical-indent-base-value: 32px; /* 每级缩进32px，与之前的自定义实现保持一致 */
                }

                .lexical-indent {
                    --lexical-indent-base-value: 32px;
                }

                /* 缩进过渡动画 */
                .editor-input p[style*="margin-left"],
                .editor-input h1[style*="margin-left"],
                .editor-input h2[style*="margin-left"],
                .editor-input h3[style*="margin-left"],
                .editor-input h4[style*="margin-left"],
                .editor-input h5[style*="margin-left"],
                .editor-input h6[style*="margin-left"],
                .editor-input li[style*="margin-left"] {
                    transition: margin-left 0.2s ease;
                }

                .editor-placeholder {
                    color: #999;
                    overflow: hidden;
                    position: absolute;
                    text-overflow: ellipsis;
                    top: 1rem;
                    left: 0;
                    font-size: 1rem;
                    user-select: none;
                    display: inline-block;
                    pointer-events: none;
                }

                .editor-paragraph {
                    margin: 1.2rem 0;  /* 增加段落间距 */
                    line-height: 1.8;  /* 增加行高 */
                }

                .editor-heading-h1 {
                    font-size: 2.8em;
                    font-weight: bold;
                    margin: 1.5rem 0 1rem 0;
                    line-height: 1.2;
                }

                .editor-heading-h2 {
                    font-size: 2.2em;
                    font-weight: bold;
                    margin: 1.4rem 0 0.8rem 0;
                    line-height: 1.3;
                }

                .editor-heading-h3 {
                    font-size: 1.8em;
                    font-weight: bold;
                    margin: 1.3rem 0 0.6rem 0;
                    line-height: 1.4;
                }

                .editor-heading-h4 {
                    font-size: 1.5em;
                    font-weight: bold;
                    margin: 1.2rem 0 0.5rem 0;
                    line-height: 1.4;
                }

                .editor-heading-h5 {
                    font-size: 1.3em;
                    font-weight: bold;
                    margin: 1.1rem 0 0.4rem 0;
                    line-height: 1.5;
                }

                .editor-heading-h6 {
                    font-size: 1.2em;
                    font-weight: bold;
                    margin: 1rem 0 0.3rem 0;
                    line-height: 1.5;
                }

                .editor-quote {
                    margin: 1rem 0;
                    padding-left: 1rem;
                    border-left: 4px solid #ccc;
                    font-style: italic;
                    color: #666;
                }

                .editor-list-ol,
                .editor-list-ul {
                    margin: 1rem 0;
                    padding-left: 2rem;
                }

                .editor-listitem {
                    margin: 0.5rem 0;
                }

                .editor-link {
                    color: #3b82f6;
                    text-decoration: underline;
                    cursor: pointer;
                }

                .editor-link:hover {
                    color: #1d4ed8;
                }

                .editor-text-bold {
                    font-weight: bold;
                }

                .editor-text-italic {
                    font-style: italic;
                }

                .editor-text-underline {
                    text-decoration: underline;
                }

                .editor-text-strikethrough {
                    text-decoration: line-through;
                }

                .editor-text-code {
                    background-color: #e4e4e7;
                    color: black;
                    padding: 0.2rem 0.4rem;
                    border-radius: 0.25rem;
                    font-family: monospace;
                    font-size: 0.9em;
                }

                .editor-code {
                    background-color: #e4e4e7;
                    color: black;
                    border: 1px solid #e9ecef;
                    border-radius: 0.375rem;
                    padding: 1rem;
                    margin: 1rem 0;
                    font-family: 'Courier New', Courier, monospace;
                    font-size: 0.9em;
                    line-height: 1.4;
                    overflow-x: auto;
                }

                /* 深色主题下的代码样式 */
                [data-theme="dark"] .editor-text-code {
                    background-color: #3f3f46;
                    color: white;
                }

                [data-theme="dark"] .editor-code {
                    background-color: #3f3f46;
                    color: white;
                    border-color: #4b5563;
                }

                /* 基础列表样式 - 确保符号显示 */
                .editor-list-ol {
                    list-style-type: decimal;
                    margin: 0.5rem 0;
                    padding-left: 1.5rem;
                }

                .editor-list-ul {
                    list-style-type: disc;
                    margin: 0.5rem 0;
                    padding-left: 1.5rem;
                }

                .editor-listitem {
                    margin: 0.4rem 0;  /* 增加列表项间距 */
                    line-height: 1.8;  /* 增加行高 */
                }

                .editor-nested-listitem {
                    margin: 0.3rem 0;  /* 嵌套列表项间距 */
                }

                /* 有序列表按深度定义不同符号 - 关键！ */
                .editor-list-ol-1 { list-style-type: decimal; }      /* 1, 2, 3... */
                .editor-list-ol-2 { list-style-type: lower-alpha; }  /* a, b, c... */
                .editor-list-ol-3 { list-style-type: lower-roman; }  /* i, ii, iii... */
                .editor-list-ol-4 { list-style-type: upper-alpha; }  /* A, B, C... */
                .editor-list-ol-5 { list-style-type: upper-roman; }  /* I, II, III... */

                /* 无序列表按深度定义不同符号 */
                .editor-list-ul-1 { list-style-type: disc; }         /* • */
                .editor-list-ul-2 { list-style-type: circle; }       /* ○ */
                .editor-list-ul-3 { list-style-type: square; }       /* ▪ */
                .editor-list-ul-4 { list-style-type: disc; }         /* • */
                .editor-list-ul-5 { list-style-type: circle; }       /* ○ */



                /* Task List Styles */
                .task-list {
                    list-style: none;
                    padding-left: 0;
                    margin: 1rem 0;
                }

                .task-item {
                    display: flex;
                    align-items: flex-start;
                    margin: 0.5rem 0;
                    list-style: none;
                }

                .task-checkbox {
                    margin-right: 0.5rem;
                    margin-top: 0.125rem;
                    cursor: pointer;
                    width: 1rem;
                    height: 1rem;
                    border: 1px solid #d1d5db;
                    border-radius: 0.25rem;
                    background: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    appearance: none;
                    -webkit-appearance: none;
                }

                .task-checkbox:checked {
                    background-color: #3b82f6;
                    border-color: #3b82f6;
                    color: white;
                }

                .task-checkbox:checked::after {
                    content: '✓';
                    font-size: 0.75rem;
                    font-weight: bold;
                    color: white;
                }

                .task-content {
                    flex: 1;
                    line-height: 1.7;
                }

                .task-item[data-checked="true"] .task-content {
                    text-decoration: line-through;
                    opacity: 0.6;
                }

                /* Highlight Styles - 支持Lexical内置的highlight格式 */
                .lexical-highlight,
                mark,
                .editor-text-highlight {
                    background-color: #eab834 !important;
                    color: black !important;
                    padding: 0.1rem 0.2rem;
                    border-radius: 0.125rem;
                }

                /* 深色主题下的高亮样式 - 支持多种主题类名 */
                .dark .lexical-highlight,
                .dark mark,
                .dark .editor-text-highlight,
                [data-theme="dark"] .lexical-highlight,
                [data-theme="dark"] mark,
                [data-theme="dark"] .editor-text-highlight,
                html.dark .lexical-highlight,
                html.dark mark,
                html.dark .editor-text-highlight {
                    background-color: #3185eb !important;
                    color: white !important;
                }

                /* Image Styles */
                .editor-image img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 0.5rem;
                    margin: 1rem 0;
                }

                /* Dark mode support */
                @media (prefers-color-scheme: dark) {
                    .editor-placeholder {
                        color: #6b7280;
                    }

                    .editor-quote {
                        border-left-color: #4b5563;
                        color: #9ca3af;
                    }

                    .editor-text-code {
                        background-color: #374151;
                        color: #f9fafb;
                    }

                    .editor-code {
                        background-color: #1f2937;
                        border-color: #374151;
                        color: #f9fafb;
                    }


                }

                /* 列表项符号样式 - 灰色（除了checkbox） */
                .lexical-editor ul:not([data-lexical-list-type="check"]) li::marker {
                    color: #6b7280;
                }

                .lexical-editor ol li::marker {
                    color: #6b7280;
                }

                /* Lexical原生CheckList样式 - 基于PlaygroundEditorTheme.css */
                .lexical-editor .PlaygroundEditorTheme__listItemChecked,
                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked {
                    position: relative;
                    margin-left: 0.5em;
                    margin-right: 0.5em;
                    padding-left: 1.5em;
                    padding-right: 1.5em;
                    list-style-type: none;
                    outline: none;
                    display: block;
                    min-height: 1.5em;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked > *,
                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked > * {
                    margin-left: 0.01em;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,
                .lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
                    content: '';
                    width: 0.9em;
                    height: 0.9em;
                    top: 50%;
                    left: 0;
                    cursor: pointer;
                    display: block;
                    background-size: cover;
                    position: absolute;
                    transform: translateY(-50%);
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked {
                    text-decoration: line-through;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,
                .lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before {
                    box-shadow: 0 0 0 2px #a6cdfe;
                    border-radius: 2px;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before {
                    border: 1px solid #999;
                    border-radius: 2px;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
                    border: 1px solid rgb(61, 135, 245);
                    border-radius: 2px;
                    background-color: #3d87f5;
                    background-repeat: no-repeat;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked:after {
                    content: '';
                    cursor: pointer;
                    border-color: #fff;
                    border-style: solid;
                    position: absolute;
                    display: block;
                    top: 45%;
                    width: 0.2em;
                    left: 0.35em;
                    height: 0.4em;
                    transform: translateY(-50%) rotate(45deg);
                    border-width: 0 0.1em 0.1em 0;
                }

                /* 移除了CSS伪装的checkbox - 现在使用真正的Lexical CheckList功能 */
            `}</style>
        </div>
    );
});

LexicalEditor.displayName = 'LexicalEditor';

export default LexicalEditor;
