"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 🔧 调试：检查onChange回调是否正确传递\n    console.log(\"\\uD83D\\uDD27 LexicalEditor 初始化:\", {\n        hasOnChange: !!onChange,\n        readOnly: readOnly,\n        valueLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n        mounted: mounted,\n        timestamp: new Date().toISOString()\n    });\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 🔧 修复：创建有效的初始编辑器状态\n        editorState: function() {\n            var editor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                namespace: \"LexicalEditor\",\n                nodes: [\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                    _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                    _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                ],\n                onError: function(error) {\n                    return console.error(\"Lexical Error:\", error);\n                }\n            });\n            // 创建基本的编辑器状态\n            return editor.parseEditorState({\n                root: {\n                    children: [\n                        {\n                            children: [],\n                            direction: null,\n                            format: \"\",\n                            indent: 0,\n                            type: \"paragraph\",\n                            version: 1\n                        }, \n                    ],\n                    direction: null,\n                    format: \"\",\n                    indent: 0,\n                    type: \"root\",\n                    version: 1\n                }\n            });\n        }\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        console.log(\"\\uD83D\\uDEA8 handleChange被触发!\", {\n            hasOnChange: !!onChange,\n            tags: Array.from(tags),\n            editorStateExists: !!editorState,\n            timestamp: new Date().toISOString()\n        });\n        if (!onChange) {\n            console.log(\"❌ onChange函数不存在，无法处理编辑器变化\");\n            return;\n        }\n        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n        if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n            console.log(\"⏭️ 跳过历史合并或内容同步触发的更新\");\n            return;\n        }\n        console.log(\"✅ 开始处理编辑器状态变化\");\n        editorState.read(function() {\n            try {\n                // 🔧 添加调试：检查编辑器状态中的列表结构\n                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                var children = root.getChildren();\n                console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                children.forEach(function(child, index) {\n                    console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素:\"), {\n                        type: child.getType(),\n                        textContent: child.getTextContent()\n                    });\n                    if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                        console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                            type: child.getListType(),\n                            childrenCount: child.getChildren().length\n                        });\n                        var listItems = child.getChildren();\n                        listItems.forEach(function(item, itemIndex) {\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                var itemChildren = item.getChildren();\n                                console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                    childrenCount: itemChildren.length,\n                                    textContent: item.getTextContent(),\n                                    hasNestedList: itemChildren.some(function(c) {\n                                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                    })\n                                });\n                            }\n                        });\n                    }\n                });\n                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                var editorStateJSON = JSON.stringify(editorState.toJSON());\n                console.log(\"\\uD83D\\uDD27 准备调用onChange，JSON长度:\", editorStateJSON.length);\n                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑\n                onChange(function() {\n                    console.log(\"\\uD83D\\uDD27 onChange回调被执行，返回JSON内容\");\n                    return editorStateJSON;\n                });\n                console.log(\"✅ onChange调用完成\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D Error in handleChange:\", error);\n            // 如果转换出错，保持原有内容不变\n            }\n        });\n    }, [\n        onChange,\n        value\n    ]);\n    // 调试插件 - 检查编辑器状态\n    var DebugPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器初始化完成\", {\n                isEditable: editor.isEditable(),\n                hasRootElement: !!editor.getRootElement(),\n                readOnly: readOnly,\n                timestamp: new Date().toISOString()\n            });\n            // 🔧 确保编辑器在非只读模式下是可编辑的\n            if (!readOnly && !editor.isEditable()) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 强制设置编辑器为可编辑状态\");\n                editor.setEditable(true);\n            }\n            // 监听所有编辑器更新\n            var removeUpdateListener = editor.registerUpdateListener(function(param) {\n                var editorState = param.editorState, prevEditorState = param.prevEditorState, tags = param.tags;\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器更新\", {\n                    tags: Array.from(tags),\n                    hasChanges: editorState !== prevEditorState,\n                    isEditable: editor.isEditable(),\n                    timestamp: new Date().toISOString()\n                });\n            });\n            // 监听编辑器状态变化\n            var removeEditableListener = editor.registerEditableListener(function(editable) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器可编辑状态变化\", {\n                    editable: editable,\n                    readOnly: readOnly\n                });\n            });\n            return function() {\n                removeUpdateListener();\n                removeEditableListener();\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(DebugPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                console.log(\"\\uD83D\\uDD27 ListExitPlugin: Enter键被按下\");\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 优化版本，避免与用户输入冲突\n    var ContentSyncPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        var _$ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), lastSyncedValue = _$ref1[0], setLastSyncedValue = _$ref1[1];\n        var _$ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUserTyping = _$ref2[0], setIsUserTyping = _$ref2[1];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 🔧 优化：只有当value真正改变且用户没有在输入时才同步\n                if (value !== lastSyncedValue && !isUserTyping) {\n                    console.log(\"\\uD83D\\uDD27 ContentSyncPlugin: 准备同步内容\", {\n                        valueLength: value.length,\n                        lastSyncedLength: lastSyncedValue.length,\n                        isUserTyping: isUserTyping\n                    });\n                    // 使用setTimeout来避免在渲染过程中调用flushSync\n                    setTimeout(function() {\n                        if (value.trim()) {\n                            try {\n                                // 🔧 解析JSON格式的编辑器状态\n                                var editorStateData = JSON.parse(value);\n                                console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                                // 直接设置编辑器状态\n                                var newEditorState = editor.parseEditorState(editorStateData);\n                                editor.setEditorState(newEditorState);\n                                setLastSyncedValue(value);\n                            } catch (jsonError) {\n                                console.error(\"\\uD83D\\uDD27 JSON解析失败，尝试作为Markdown处理:\", jsonError);\n                                // 🔧 修复：如果不是JSON格式，尝试作为Markdown或纯文本处理\n                                if (value.trim() === \"\\n\" || value.trim() === \"\") {\n                                    // 空内容，创建空段落\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                } else {\n                                    // 有内容但不是JSON，可能是旧的Markdown格式\n                                    console.log(\"\\uD83D\\uDD27 检测到非JSON内容，可能是旧格式，创建空编辑器\");\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                }\n                            }\n                        } else {\n                            // 空内容时清空并创建一个空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                            setLastSyncedValue(value);\n                        }\n                    }, 0);\n                }\n            }\n        }, [\n            editor,\n            value,\n            mounted,\n            lastSyncedValue,\n            isUserTyping\n        ]);\n        // 🔧 监听用户输入状态\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (!editor) return;\n            var rootElement = editor.getRootElement();\n            if (!rootElement) return;\n            var typingTimer;\n            var handleInput = function() {\n                setIsUserTyping(true);\n                clearTimeout(typingTimer);\n                typingTimer = setTimeout(function() {\n                    setIsUserTyping(false);\n                }, 1000); // 1秒后认为用户停止输入\n            };\n            rootElement.addEventListener(\"input\", handleInput);\n            rootElement.addEventListener(\"keydown\", handleInput);\n            return function() {\n                rootElement.removeEventListener(\"input\", handleInput);\n                rootElement.removeEventListener(\"keydown\", handleInput);\n                clearTimeout(typingTimer);\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(ContentSyncPlugin, \"SYK262ymxGBaV3Gm+LQPP5D2LGU=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 🔧 新增：DOM状态检查插件\n    var DOMStatePlugin = function() {\n        _s4();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            var checkDOMState = function() {\n                var rootElement = editor.getRootElement();\n                if (rootElement) {\n                    console.log(\"\\uD83D\\uDD27 DOM状态检查:\", {\n                        contentEditable: rootElement.contentEditable,\n                        isContentEditable: rootElement.isContentEditable,\n                        tabIndex: rootElement.tabIndex,\n                        style: {\n                            pointerEvents: getComputedStyle(rootElement).pointerEvents,\n                            userSelect: getComputedStyle(rootElement).userSelect,\n                            cursor: getComputedStyle(rootElement).cursor,\n                            display: getComputedStyle(rootElement).display,\n                            visibility: getComputedStyle(rootElement).visibility\n                        },\n                        hasChildren: rootElement.children.length,\n                        textContent: rootElement.textContent,\n                        innerHTML: rootElement.innerHTML.substring(0, 200)\n                    });\n                    // 🔧 强制设置contentEditable\n                    if (rootElement.contentEditable !== \"true\" && !readOnly) {\n                        console.log(\"\\uD83D\\uDD27 强制设置contentEditable为true\");\n                        rootElement.contentEditable = \"true\";\n                    }\n                    // 🔧 强制设置tabIndex\n                    if (rootElement.tabIndex < 0) {\n                        console.log(\"\\uD83D\\uDD27 设置tabIndex为0\");\n                        rootElement.tabIndex = 0;\n                    }\n                }\n            };\n            // 立即检查\n            checkDOMState();\n            // 定期检查\n            var interval = setInterval(checkDOMState, 2000);\n            return function() {\n                return clearInterval(interval);\n            };\n        }, [\n            editor,\n            readOnly\n        ]);\n        return null;\n    };\n    _s4(DOMStatePlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false,\n                                style: {\n                                    userSelect: \"text\",\n                                    WebkitUserSelect: \"text\",\n                                    MozUserSelect: \"text\",\n                                    msUserSelect: \"text\",\n                                    cursor: \"text\",\n                                    minHeight: \"200px\"\n                                },\n                                onKeyDown: function(e) {\n                                    console.log(\"\\uD83C\\uDFB9 键盘按下:\", {\n                                        key: e.key,\n                                        code: e.code,\n                                        ctrlKey: e.ctrlKey,\n                                        shiftKey: e.shiftKey,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onFocus: function(e) {\n                                    console.log(\"\\uD83C\\uDFAF 编辑器获得焦点:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                },\n                                onBlur: function() {\n                                    console.log(\"\\uD83D\\uDE34 编辑器失去焦点\");\n                                },\n                                onClick: function(e) {\n                                    console.log(\"\\uD83D\\uDDB1️ 编辑器点击:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 701,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 633,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 632,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 631,\n        columnNumber: 9\n    }, _this);\n}, \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});