"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 🔧 调试：检查onChange回调是否正确传递\n    console.log(\"\\uD83D\\uDD27 LexicalEditor 初始化:\", {\n        hasOnChange: !!onChange,\n        readOnly: readOnly,\n        valueLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n        mounted: mounted,\n        timestamp: new Date().toISOString()\n    });\n    // 🔧 检查全局事件监听器和干扰\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        if (true) {\n            console.log(\"\\uD83D\\uDD27 检查全局事件监听器...\");\n            // 🔧 检查是否有全局的事件监听器\n            var originalAddEventListener = document.addEventListener;\n            var globalListeners = [];\n            // 临时拦截addEventListener来记录全局监听器\n            document.addEventListener = function(type, listener, options) {\n                if ([\n                    \"beforeinput\",\n                    \"input\",\n                    \"keydown\",\n                    \"keypress\"\n                ].includes(type)) {\n                    console.log(\"\\uD83D\\uDD27 检测到全局事件监听器:\", {\n                        type: type,\n                        listener: listener.toString().substring(0, 100),\n                        options: options\n                    });\n                    globalListeners.push({\n                        type: type,\n                        listener: listener,\n                        options: options\n                    });\n                }\n                return originalAddEventListener.call(this, type, listener, options);\n            };\n            // 恢复原始方法\n            setTimeout(function() {\n                document.addEventListener = originalAddEventListener;\n                console.log(\"\\uD83D\\uDD27 全局监听器检查完成，发现:\", globalListeners.length, \"个相关监听器\");\n            }, 1000);\n            // 检查是否有全局的beforeinput监听器\n            var testElement = document.createElement(\"div\");\n            testElement.contentEditable = \"true\";\n            testElement.style.position = \"absolute\";\n            testElement.style.top = \"-1000px\";\n            document.body.appendChild(testElement);\n            var testEvent = new InputEvent(\"beforeinput\", {\n                inputType: \"insertText\",\n                data: \"test\",\n                bubbles: true,\n                cancelable: true\n            });\n            testElement.addEventListener(\"beforeinput\", function(e) {\n                console.log(\"\\uD83D\\uDD27 测试元素接收到beforeinput事件:\", e);\n            });\n            console.log(\"\\uD83D\\uDD27 派发测试beforeinput事件...\");\n            var result = testElement.dispatchEvent(testEvent);\n            console.log(\"\\uD83D\\uDD27 测试事件结果:\", {\n                result: result,\n                defaultPrevented: testEvent.defaultPrevented\n            });\n            setTimeout(function() {\n                document.body.removeChild(testElement);\n            }, 100);\n        }\n    }, []);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 🔧 修复：创建有效的初始编辑器状态\n        editorState: function() {\n            var editor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                namespace: \"LexicalEditor\",\n                nodes: [\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                    _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                    _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                ],\n                onError: function(error) {\n                    return console.error(\"Lexical Error:\", error);\n                }\n            });\n            // 创建基本的编辑器状态\n            return editor.parseEditorState({\n                root: {\n                    children: [\n                        {\n                            children: [],\n                            direction: null,\n                            format: \"\",\n                            indent: 0,\n                            type: \"paragraph\",\n                            version: 1\n                        }, \n                    ],\n                    direction: null,\n                    format: \"\",\n                    indent: 0,\n                    type: \"root\",\n                    version: 1\n                }\n            });\n        }\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        console.log(\"\\uD83D\\uDEA8 handleChange被触发!\", {\n            hasOnChange: !!onChange,\n            tags: Array.from(tags),\n            editorStateExists: !!editorState,\n            timestamp: new Date().toISOString()\n        });\n        if (!onChange) {\n            console.log(\"❌ onChange函数不存在，无法处理编辑器变化\");\n            return;\n        }\n        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n        if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n            console.log(\"⏭️ 跳过历史合并或内容同步触发的更新\");\n            return;\n        }\n        console.log(\"✅ 开始处理编辑器状态变化\");\n        editorState.read(function() {\n            try {\n                // 🔧 添加调试：检查编辑器状态中的列表结构\n                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                var children = root.getChildren();\n                console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                children.forEach(function(child, index) {\n                    console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素:\"), {\n                        type: child.getType(),\n                        textContent: child.getTextContent()\n                    });\n                    if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                        console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                            type: child.getListType(),\n                            childrenCount: child.getChildren().length\n                        });\n                        var listItems = child.getChildren();\n                        listItems.forEach(function(item, itemIndex) {\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                var itemChildren = item.getChildren();\n                                console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                    childrenCount: itemChildren.length,\n                                    textContent: item.getTextContent(),\n                                    hasNestedList: itemChildren.some(function(c) {\n                                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                    })\n                                });\n                            }\n                        });\n                    }\n                });\n                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                var editorStateJSON = JSON.stringify(editorState.toJSON());\n                console.log(\"\\uD83D\\uDD27 准备调用onChange，JSON长度:\", editorStateJSON.length);\n                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑\n                onChange(function() {\n                    console.log(\"\\uD83D\\uDD27 onChange回调被执行，返回JSON内容\");\n                    return editorStateJSON;\n                });\n                console.log(\"✅ onChange调用完成\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D Error in handleChange:\", error);\n            // 如果转换出错，保持原有内容不变\n            }\n        });\n    }, [\n        onChange,\n        value\n    ]);\n    // 调试插件 - 检查编辑器状态\n    var DebugPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器初始化完成\", {\n                isEditable: editor.isEditable(),\n                hasRootElement: !!editor.getRootElement(),\n                readOnly: readOnly,\n                timestamp: new Date().toISOString()\n            });\n            // 🔧 确保编辑器在非只读模式下是可编辑的\n            if (!readOnly && !editor.isEditable()) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 强制设置编辑器为可编辑状态\");\n                editor.setEditable(true);\n            }\n            // 监听所有编辑器更新\n            var removeUpdateListener = editor.registerUpdateListener(function(param) {\n                var editorState = param.editorState, prevEditorState = param.prevEditorState, tags = param.tags;\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器更新\", {\n                    tags: Array.from(tags),\n                    hasChanges: editorState !== prevEditorState,\n                    isEditable: editor.isEditable(),\n                    timestamp: new Date().toISOString()\n                });\n            });\n            // 监听编辑器状态变化\n            var removeEditableListener = editor.registerEditableListener(function(editable) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器可编辑状态变化\", {\n                    editable: editable,\n                    readOnly: readOnly\n                });\n            });\n            return function() {\n                removeUpdateListener();\n                removeEditableListener();\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(DebugPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                console.log(\"\\uD83D\\uDD27 ListExitPlugin: Enter键被按下\");\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 优化版本，避免与用户输入冲突\n    var ContentSyncPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        var _$ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), lastSyncedValue = _$ref1[0], setLastSyncedValue = _$ref1[1];\n        var _$ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUserTyping = _$ref2[0], setIsUserTyping = _$ref2[1];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 🔧 优化：只有当value真正改变且用户没有在输入时才同步\n                if (value !== lastSyncedValue && !isUserTyping) {\n                    console.log(\"\\uD83D\\uDD27 ContentSyncPlugin: 准备同步内容\", {\n                        valueLength: value.length,\n                        lastSyncedLength: lastSyncedValue.length,\n                        isUserTyping: isUserTyping\n                    });\n                    // 使用setTimeout来避免在渲染过程中调用flushSync\n                    setTimeout(function() {\n                        if (value.trim()) {\n                            try {\n                                // 🔧 解析JSON格式的编辑器状态\n                                var editorStateData = JSON.parse(value);\n                                console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                                // 直接设置编辑器状态\n                                var newEditorState = editor.parseEditorState(editorStateData);\n                                editor.setEditorState(newEditorState);\n                                setLastSyncedValue(value);\n                            } catch (jsonError) {\n                                console.error(\"\\uD83D\\uDD27 JSON解析失败，尝试作为Markdown处理:\", jsonError);\n                                // 🔧 修复：如果不是JSON格式，尝试作为Markdown或纯文本处理\n                                if (value.trim() === \"\\n\" || value.trim() === \"\") {\n                                    // 空内容，创建空段落\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                } else {\n                                    // 有内容但不是JSON，可能是旧的Markdown格式\n                                    console.log(\"\\uD83D\\uDD27 检测到非JSON内容，可能是旧格式，创建空编辑器\");\n                                    editor.update(function() {\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    });\n                                    setLastSyncedValue(value);\n                                }\n                            }\n                        } else {\n                            // 空内容时清空并创建一个空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                            setLastSyncedValue(value);\n                        }\n                    }, 0);\n                }\n            }\n        }, [\n            editor,\n            value,\n            mounted,\n            lastSyncedValue,\n            isUserTyping\n        ]);\n        // 🔧 监听用户输入状态\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (!editor) return;\n            var rootElement = editor.getRootElement();\n            if (!rootElement) return;\n            var typingTimer;\n            var handleInput = function() {\n                setIsUserTyping(true);\n                clearTimeout(typingTimer);\n                typingTimer = setTimeout(function() {\n                    setIsUserTyping(false);\n                }, 1000); // 1秒后认为用户停止输入\n            };\n            rootElement.addEventListener(\"input\", handleInput);\n            rootElement.addEventListener(\"keydown\", handleInput);\n            return function() {\n                rootElement.removeEventListener(\"input\", handleInput);\n                rootElement.removeEventListener(\"keydown\", handleInput);\n                clearTimeout(typingTimer);\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(ContentSyncPlugin, \"SYK262ymxGBaV3Gm+LQPP5D2LGU=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 🔧 新增：DOM状态检查插件\n    var DOMStatePlugin = function() {\n        _s4();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            var checkDOMState = function() {\n                var rootElement = editor.getRootElement();\n                if (rootElement) {\n                    console.log(\"\\uD83D\\uDD27 DOM状态检查:\", {\n                        contentEditable: rootElement.contentEditable,\n                        isContentEditable: rootElement.isContentEditable,\n                        tabIndex: rootElement.tabIndex,\n                        style: {\n                            pointerEvents: getComputedStyle(rootElement).pointerEvents,\n                            userSelect: getComputedStyle(rootElement).userSelect,\n                            cursor: getComputedStyle(rootElement).cursor,\n                            display: getComputedStyle(rootElement).display,\n                            visibility: getComputedStyle(rootElement).visibility\n                        },\n                        hasChildren: rootElement.children.length,\n                        textContent: rootElement.textContent,\n                        innerHTML: rootElement.innerHTML.substring(0, 200)\n                    });\n                    // 🔧 强制设置contentEditable\n                    if (rootElement.contentEditable !== \"true\" && !readOnly) {\n                        console.log(\"\\uD83D\\uDD27 强制设置contentEditable为true\");\n                        rootElement.contentEditable = \"true\";\n                    }\n                    // 🔧 强制设置tabIndex\n                    if (rootElement.tabIndex < 0) {\n                        console.log(\"\\uD83D\\uDD27 设置tabIndex为0\");\n                        rootElement.tabIndex = 0;\n                    }\n                    // 🔧 检查是否有全局事件监听器干扰\n                    var testInput = function() {\n                        console.log(\"\\uD83D\\uDD27 测试输入功能...\");\n                        rootElement.focus();\n                        // 🔧 检查元素层级和覆盖情况\n                        var rect = rootElement.getBoundingClientRect();\n                        var elementAtPoint = document.elementFromPoint(rect.left + rect.width / 2, rect.top + rect.height / 2);\n                        console.log(\"\\uD83D\\uDD27 编辑器位置检查:\", {\n                            editorRect: rect,\n                            elementAtCenter: elementAtPoint,\n                            isEditorAtCenter: elementAtPoint === rootElement || rootElement.contains(elementAtPoint),\n                            zIndex: getComputedStyle(rootElement).zIndex,\n                            position: getComputedStyle(rootElement).position\n                        });\n                        // 🔧 检查父元素是否有阻止事件的样式\n                        var parent = rootElement.parentElement;\n                        while(parent && parent !== document.body){\n                            var parentStyle = getComputedStyle(parent);\n                            if (parentStyle.pointerEvents === \"none\" || parentStyle.userSelect === \"none\" || parentStyle.position === \"absolute\" && parentStyle.zIndex) {\n                                console.log(\"\\uD83D\\uDD27 发现可能有问题的父元素:\", {\n                                    element: parent,\n                                    className: parent.className,\n                                    pointerEvents: parentStyle.pointerEvents,\n                                    userSelect: parentStyle.userSelect,\n                                    zIndex: parentStyle.zIndex,\n                                    position: parentStyle.position\n                                });\n                            }\n                            parent = parent.parentElement;\n                        }\n                        // 模拟键盘输入\n                        var event = new KeyboardEvent(\"keydown\", {\n                            key: \"a\",\n                            code: \"KeyA\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        var inputEvent = new InputEvent(\"beforeinput\", {\n                            inputType: \"insertText\",\n                            data: \"a\",\n                            bubbles: true,\n                            cancelable: true\n                        });\n                        console.log(\"\\uD83D\\uDD27 派发测试事件...\");\n                        var keyResult = rootElement.dispatchEvent(event);\n                        var inputResult = rootElement.dispatchEvent(inputEvent);\n                        console.log(\"\\uD83D\\uDD27 事件派发结果:\", {\n                            keyEvent: keyResult,\n                            inputEvent: inputResult,\n                            keyDefaultPrevented: event.defaultPrevented,\n                            inputDefaultPrevented: inputEvent.defaultPrevented\n                        });\n                    };\n                    // 延迟测试，确保编辑器完全初始化\n                    setTimeout(testInput, 1000);\n                }\n            };\n            // 立即检查\n            checkDOMState();\n            // 定期检查\n            var interval = setInterval(checkDOMState, 5000);\n            return function() {\n                return clearInterval(interval);\n            };\n        }, [\n            editor,\n            readOnly\n        ]);\n        return null;\n    };\n    _s4(DOMStatePlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false,\n                                style: {\n                                    userSelect: \"text\",\n                                    WebkitUserSelect: \"text\",\n                                    MozUserSelect: \"text\",\n                                    msUserSelect: \"text\",\n                                    cursor: \"text\",\n                                    minHeight: \"200px\",\n                                    // 🔧 强制设置可编辑相关样式\n                                    pointerEvents: \"auto\",\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                onKeyDown: function(e) {\n                                    console.log(\"\\uD83C\\uDFB9 键盘按下:\", {\n                                        key: e.key,\n                                        code: e.code,\n                                        ctrlKey: e.ctrlKey,\n                                        shiftKey: e.shiftKey,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                    // 🔧 检查事件是否被阻止\n                                    if (e.defaultPrevented) {\n                                        console.warn(\"⚠️ 键盘事件被阻止了!\");\n                                    }\n                                },\n                                onInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onBeforeInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD beforeinput事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        defaultPrevented: e.defaultPrevented,\n                                        target: e.target,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                    // 🔧 检查beforeinput事件是否被阻止\n                                    if (e.defaultPrevented) {\n                                        console.warn(\"⚠️ beforeinput事件被阻止了!\");\n                                    }\n                                },\n                                onFocus: function(e) {\n                                    console.log(\"\\uD83C\\uDFAF 编辑器获得焦点:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                },\n                                onBlur: function() {\n                                    console.log(\"\\uD83D\\uDE34 编辑器失去焦点\");\n                                },\n                                onClick: function(e) {\n                                    console.log(\"\\uD83D\\uDDB1️ 编辑器点击:\", {\n                                        target: e.target,\n                                        contentEditable: e.target.contentEditable,\n                                        isContentEditable: e.target.isContentEditable\n                                    });\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 839,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 844,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 845,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DOMStatePlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 857,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 761,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 760,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 759,\n        columnNumber: 9\n    }, _this);\n}, \"iuZ+b0O1CXZIV7Hez3f+DTYXPqg=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"iuZ+b0O1CXZIV7Hez3f+DTYXPqg=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});