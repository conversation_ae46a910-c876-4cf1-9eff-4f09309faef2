{"version": 3, "sources": ["../../../build/analysis/parse-module.ts"], "names": ["parseModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "L<PERSON><PERSON><PERSON>", "max", "filename", "content", "parse", "isModule", "catch", "_", "createHash", "update", "digest"], "mappings": "AAAA;;;;;AAAqB,IAAA,SAA8B,kCAA9B,8BAA8B,EAAA;AAClB,IAAA,iBAA8B,WAA9B,8BAA8B,CAAA;AACpC,IAAA,OAAQ,WAAR,QAAQ,CAAA;AACb,IAAA,IAAQ,WAAR,QAAQ,CAAA;;;;;;AAMvB,MAAMA,WAAW,GAAGC,CAAAA,GAAAA,iBAAgB,AAK1C,CAAA,iBAL0C,CACzC,IAAIC,SAAQ,QAAA,CAAc;IAAEC,GAAG,EAAE,GAAG;CAAE,CAAC,EACvC,OAAOC,QAAgB,EAAEC,OAAe,GACtCC,CAAAA,GAAAA,IAAK,AAA4C,CAAA,MAA5C,CAACD,OAAO,EAAE;QAAEE,QAAQ,EAAE,SAAS;QAAEH,QAAQ;KAAE,CAAC,CAACI,KAAK,CAAC,IAAM,IAAI,CAAC,EACrE,CAACC,CAAC,EAAEJ,OAAO,GAAKK,CAAAA,GAAAA,OAAU,AAAQ,CAAA,WAAR,CAAC,MAAM,CAAC,CAACC,MAAM,CAACN,OAAO,CAAC,CAACO,MAAM,CAAC,KAAK,CAAC,CACjE;QALYZ,WAAW,GAAXA,WAAW"}